"use client";

import { usePasswordModify } from "@/service/auth-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

export function PasswordChangeDialog({ open, onClose }: { open: boolean; onClose: () => void }) {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const passwordModifyMutation = usePasswordModify();
  const { toast } = useToast();

  // 表单验证模式
  const formSchema = z
    .object({
      newPassword: z.string().min(6, t("password.minLength")),
      confirmPassword: z.string().min(6, t("password.confirmMinLength")),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: t("password.mismatch"),
      path: ["confirmPassword"],
    });

  type FormValues = z.infer<typeof formSchema>;

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  const handleClose = () => {
    form.reset();
    onClose();
  };

  // 处理密码修改
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true);
    setError("");

    passwordModifyMutation.mutate(
      { new_password: values.newPassword },
      {
        onSuccess: () => {
          toast({
            title: t("password.changeSuccess"),
          });
          form.reset();
          handleClose();
        },
        onError: () => {
          setError(t("password.changeFail"));
          setIsLoading(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("password.changeTitle")}</DialogTitle>
          <DialogDescription>{t("password.changeDescription")}</DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-destructive/15 text-destructive mb-2 rounded-md p-3 text-sm">
            {error}
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("password.newPassword")}</RequiredFormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t("password.newPasswordPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("password.confirmNewPassword")}</RequiredFormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t("password.confirmNewPasswordPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    {t("common.submitting")}
                  </span>
                ) : (
                  t("password.confirmChange")
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
