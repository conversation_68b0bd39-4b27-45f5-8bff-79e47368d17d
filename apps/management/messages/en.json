{"common": {"confirm": "Confirm", "delete": "Delete", "edit": "Edit", "save": "Save", "submit": "Submit", "submitting": "Submitting...", "add": "Add", "create": "Create", "update": "Update", "view": "View", "details": "Details", "name": "Name", "username": "Username", "password": "Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "description": "Description", "time": "Time", "actions": "Actions", "status": "Status", "success": "Success", "error": "Error", "failed": "Failed", "retry": "Retry", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "download": "Download", "upload": "Upload", "export": "Export", "import": "Import", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "rememberMe": "Remember Me", "loginWith": "Login with {provider}", "or": "or", "and": "and", "yes": "Yes", "no": "No", "ok": "OK", "cancel": "Cancel", "apply": "Apply", "reset": "Reset", "clear": "Clear", "select": "Select", "deselect": "Deselect", "selectAll": "Select All", "deselectAll": "Deselect All", "loading": "Loading...", "noData": "No data", "noResults": "No results", "empty": "Empty", "required": "Required", "optional": "Optional", "invalid": "Invalid", "valid": "<PERSON><PERSON>", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "public": "Public", "private": "Private", "visible": "Visible", "hidden": "Hidden", "true": "Yes", "false": "No", "on": "On", "off": "Off"}, "nav": {"dashboard": "Dashboard", "models": "Models", "teams": "Teams", "users": "Users"}, "teams": {"member": "Member", "noTeamMembers": "No team members yet", "teamMembers": "Team Members", "create": {"fail": {"tips": "Failed to create team"}}, "update": {"fail": {"title": "Failed to update team"}}, "delete": {"fail": {"title": "Failed to delete team"}, "confirm": "Are you sure you want to delete the team \"{name}\"? This action cannot be undone."}, "name": "Team Name", "admin": "Admin", "memberCount": "Members", "createdAt": "Created At", "details": "Team Details", "createTeam": "Create Team", "editTeam": "Edit Team", "unassigned": "Unassigned", "selectAdmin": "Select Team Admin", "selectedAdminCount": "{count} admin(s) selected", "memberName": "Member Name", "role": "Role", "searchUser": "Search users...", "selectChatModel": "Please select a valid chat model", "selectEmbedModel": "Please select a valid embed model", "selectRerankModel": "Please select a valid rerank model", "selectNL2SQLModel": "Please select a valid NL2SQL model", "selectNL2PythonModel": "Please select a valid NL2Python model", "form": {"teamNameRequired": "Team name cannot be empty", "adminRequired": "Please select at least one team admin", "chatModelRequired": "Please select a valid model", "embdModelRequired": "Please select a valid model", "rerankModelRequired": "Please select a valid model", "nl2sqlModelRequired": "Please select a valid model", "nl2pythonModelRequired": "Please select a valid model", "teamNamePlaceholder": "Enter team name", "teamNameLabel": "Team Name", "adminLabel": "Team Admin", "adminDescription": "Team admins have permission to manage team members", "selectedAdminCount": "{count} admin(s) selected", "selectAdminPlaceholder": "Select team admin", "searchUserPlaceholder": "Search users...", "chatModelLabel": "Chat Model", "chatModelPlaceholder": "Please select a valid chat model", "embdModelLabel": "Embedding Model", "embdModelPlaceholder": "Please select a valid embedding model", "rerankModelLabel": "<PERSON><PERSON>", "rerankModelPlaceholder": "Please select a valid rerank model", "nl2sqlModelLabel": "NL2SQL Model", "nl2sqlModelPlaceholder": "Please select a valid NL2SQL model", "nl2pythonModelLabel": "NL2Python Model", "nl2pythonModelPlaceholder": "Please select a valid NL2Python model", "createButton": "Create Team", "saveButton": "Save Changes", "loadingUsers": "Failed to load users", "saveTeamFailed": "Failed to save team", "unknownUser": "User-{id}", "loading": "Loading...", "searching": "Searching...", "noUsersFound": "No users found", "loadingMore": "Loading more...", "scrollToLoadMore": "Scroll down to load more", "allUsersLoaded": "All users loaded"}}, "users": {"create": {"fail": {"title": "Failed to create user"}}, "update": {"fail": {"title": "Failed to update user"}}, "delete": {"fail": {"title": "Failed to delete user"}, "confirm": "Are you sure you want to delete the account \"{login_name}\"? This action cannot be undone."}, "loginName": "Account Name", "createdAt": "Created At", "actions": "Actions", "manage": "Account Management", "createUser": "Create Account", "editUser": "Edit Account", "accountName": "Account Name", "accountNameRequired": "Account name cannot be empty", "passwordRequired": "Password must be at least 6 characters", "enterAccountName": "Please enter account name", "inputAccountName": "Enter account name", "inputPassword": "Enter password", "leaveEmptyForNoChange": "Leave empty to keep unchanged", "noChangePasswordHint": "If you don't need to change the password, please leave it empty", "createAccount": "Create Account", "saveChanges": "Save Changes", "modifyPassword": "Modify Password"}, "models": {"apiKeyPlaceholder": "Enter API Key", "baseUrlPlaceholder": "Enter Base URL", "configSuccess": "Model configured successfully", "configFail": "Model configuration failed, please check your input and try again", "deleteSuccess": "Model deleted successfully", "deleteFail": "Failed to delete model", "editConfig": "Edit Config", "deleteModel": "Delete Model", "confirmDelete": "Are you sure you want to delete the model \"{model}\"? This action cannot be undone.", "config": "Model Config", "addLLM": "Add LLM", "modelSettings": "Model Settings", "selectModelType": "Select model type", "apiKeyRequired": "API Key cannot be empty", "baseUrlRequired": "Base URL cannot be empty", "modelPurposeRequired": "Model purpose cannot be empty", "modelNameRequired": "Model name cannot be empty", "apiVersionRequired": "API version cannot be empty", "maxTokensRequired": "Please select a valid max token count", "modelAliasRequired": "Model alias cannot be empty", "modelTypeRequired": "Model type cannot be empty", "inputModelAlias": "Enter model alias", "inputModelType": "Enter model type", "inputModelPurpose": "Enter model purpose", "inputModelName": "Enter model name", "selectModelPurpose": "Select model purpose", "configuredModels": "Configured Models", "unconfiguredModels": "Unconfigured Models"}, "login": {"title": "<PERSON><PERSON>", "description": "Please enter your username and password to log in", "usernameLabel": "Username", "usernamePlaceholder": "Enter username", "passwordLabel": "Password", "passwordPlaceholder": "Enter password", "loggingIn": "Logging in...", "login": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed, please try again", "verifying": "Verifying identity...", "usernameMinLength": "Username must be at least 2 characters"}, "password": {"changeSuccess": "Password changed successfully", "changeFail": "Failed to change password", "changeTitle": "Change Super Admin Password", "changeDescription": "Please enter your current and new password to proceed", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Re-enter new password", "mismatch": "New password and confirmation do not match", "minLength": "New password must be at least 6 characters", "confirmMinLength": "Confirmation password must be at least 6 characters", "confirmChange": "Confirm Change"}, "form": {"required": "Required", "emptyTeamName": "Team name cannot be empty", "emptyAdmin": "Please select at least one team admin", "emptyModel": "Please select a valid model", "emptyLoginName": "Admin login username cannot be empty", "emptyLoginPassword": "Admin login password cannot be empty", "emptySiteName": "Site name cannot be empty", "enterTeamName": "Please enter team name", "inputTeamName": "Enter team name", "selectAdminRequired": "Please select at least one team admin"}, "site": {"title": "{name} - Get insights quickly from knowledge and data", "description": "{name} is an AI service centered on personal and enterprise datasets, designed to unlock the full potential of your data.", "configUpdateSuccess": "Site configuration updated successfully", "configUpdateFail": "Failed to update site configuration", "fileUploadSuccess": "File uploaded successfully", "fileUploadFail": "File upload failed", "initLoginName": "Please initialize admin login username", "initLoginPassword": "Please initialize admin login password", "inputSiteName": "Please enter site name"}, "sidebar": {"user": "Account Management", "team": "Team Management", "model": "Model Config"}, "misc": {"addUser": "Add User", "edit": "Edit", "delete": "Delete", "view": "View", "confirmDelete": "Confirm Delete", "unassigned": "Unassigned"}}