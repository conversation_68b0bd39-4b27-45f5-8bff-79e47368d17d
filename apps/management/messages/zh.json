{"common": {"confirm": "确认", "delete": "删除", "edit": "编辑", "save": "保存", "submit": "提交", "submitting": "提交中...", "add": "添加", "create": "创建", "update": "更新", "view": "查看", "details": "详情", "name": "名称", "username": "用户名", "password": "密码", "newPassword": "新密码", "confirmPassword": "确认新密码", "description": "描述", "time": "时间", "actions": "操作", "status": "状态", "success": "成功", "error": "错误", "failed": "失败", "retry": "重试", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "search": "搜索", "filter": "筛选", "sort": "排序", "refresh": "刷新", "download": "下载", "upload": "上传", "export": "导出", "import": "导入", "settings": "设置", "profile": "个人资料", "logout": "退出登录", "login": "登录", "register": "注册", "forgotPassword": "忘记密码", "rememberMe": "记住我", "loginWith": "使用 {provider} 登录", "or": "或", "and": "和", "yes": "是", "no": "否", "ok": "确定", "cancel": "取消", "apply": "应用", "reset": "重置", "clear": "清除", "select": "选择", "deselect": "取消选择", "selectAll": "全选", "deselectAll": "取消全选", "loading": "加载中...", "noData": "暂无数据", "noResults": "暂无结果", "empty": "空", "required": "必填", "optional": "可选", "invalid": "无效", "valid": "有效", "enabled": "启用", "disabled": "禁用", "active": "活跃", "inactive": "非活跃", "online": "在线", "offline": "离线", "public": "公开", "private": "私有", "visible": "可见", "hidden": "隐藏", "true": "是", "false": "否", "on": "开", "off": "关"}, "nav": {"dashboard": "仪表板", "models": "模型", "teams": "团队", "users": "用户"}, "teams": {"member": "成员", "noTeamMembers": "暂无团队成员", "teamMembers": "团队成员", "create": {"fail": {"tips": "创建团队失败"}}, "update": {"fail": {"title": "更新团队失败"}}, "delete": {"fail": {"title": "删除团队失败"}, "confirm": "您确定要删除团队 \"{name}\" 吗？此操作无法撤销。"}, "name": "团队名称", "admin": "管理员", "memberCount": "成员数量", "createdAt": "创建时间", "details": "团队详情", "createTeam": "创建团队", "editTeam": "编辑团队", "unassigned": "未指定", "selectAdmin": "选择团队管理员", "selectedAdminCount": "已选择 {count} 个管理员", "memberName": "成员名称", "role": "角色", "searchUser": "搜索用户...", "selectChatModel": "请选择适当的聊天模型", "selectEmbedModel": "请选择适当的嵌入模型", "selectRerankModel": "请选择适当的Rerank模型", "selectNL2SQLModel": "请选择适当的NL2SQL模型", "selectNL2PythonModel": "请选择适当的NL2PYTHON模型", "form": {"teamNameRequired": "团队名称不能为空", "adminRequired": "请至少选择一个团队管理员", "chatModelRequired": "请选择适当的模型", "embdModelRequired": "请选择适当的模型", "rerankModelRequired": "请选择适当的模型", "nl2sqlModelRequired": "请选择适当的模型", "nl2pythonModelRequired": "请选择适当的模型", "teamNamePlaceholder": "输入团队名称", "teamNameLabel": "团队名称", "adminLabel": "团队管理员", "adminDescription": "团队管理员拥有管理团队成员的权限", "selectedAdminCount": "已选择 {count} 个管理员", "selectAdminPlaceholder": "选择团队管理员", "searchUserPlaceholder": "搜索用户...", "chatModelLabel": "聊天模型", "chatModelPlaceholder": "请选择适当的聊天模型", "embdModelLabel": "嵌入模型", "embdModelPlaceholder": "请选择适当的嵌入模型", "rerankModelLabel": "<PERSON><PERSON>模型", "rerankModelPlaceholder": "请选择适当的Rerank模型", "nl2sqlModelLabel": "NL2SQL模型", "nl2sqlModelPlaceholder": "请选择适当的NL2SQL模型", "nl2pythonModelLabel": "NL2PYTHON模型", "nl2pythonModelPlaceholder": "请选择适当的NL2PYTHON模型", "createButton": "创建团队", "saveButton": "保存修改", "loadingUsers": "加载用户失败", "saveTeamFailed": "保存团队失败", "unknownUser": "用户-{id}", "loading": "加载中...", "searching": "搜索中...", "noUsersFound": "没有找到用户", "loadingMore": "加载更多...", "scrollToLoadMore": "向下滚动加载更多", "allUsersLoaded": "已加载全部用户"}}, "users": {"create": {"fail": {"title": "创建用户失败"}}, "update": {"fail": {"title": "更新用户失败"}}, "delete": {"fail": {"title": "删除用户失败"}, "confirm": "您确定要删除账户 \"{login_name}\" 吗？此操作无法撤销。"}, "loginName": "账户名", "createdAt": "创建时间", "actions": "操作", "manage": "账户管理", "createUser": "创建账户", "editUser": "编辑账户", "accountName": "账户名", "accountNameRequired": "账户名不能为空", "passwordRequired": "密码至少需要6个字符", "enterAccountName": "请输入账户名", "inputAccountName": "输入账户名", "inputPassword": "输入密码", "leaveEmptyForNoChange": "留空表示不修改密码", "noChangePasswordHint": "如果不需要修改密码，请留空", "createAccount": "创建账户", "saveChanges": "保存修改", "modifyPassword": "修改密码"}, "models": {"apiKeyPlaceholder": "输入API Key", "baseUrlPlaceholder": "输入基础URL", "configuredModels": "已配置的模型", "unconfiguredModels": "待配置的模型", "configSuccess": "模型配置成功", "configFail": "模型配置失败，请检查输入并重试", "deleteSuccess": "删除模型成功", "deleteFail": "删除模型失败", "editConfig": "修改配置", "deleteModel": "删除模型", "confirmDelete": "您确定要删除模型 \"{model}\" 吗？此操作无法撤销。", "config": "模型配置", "addLLM": "添加LLM", "modelSettings": "模型设置", "selectModelType": "选择模型类型", "apiKeyRequired": "API Key 不能为空", "baseUrlRequired": "基础URL不能为空", "modelPurposeRequired": "模型用途不能为空", "modelNameRequired": "模型名称不能为空", "apiVersionRequired": "API版本不能为空", "maxTokensRequired": "请选择有效的最大Token数", "modelAliasRequired": "模型别名不能为空", "modelTypeRequired": "模型类型不能为空", "inputModelAlias": "请输入模型别名", "inputModelType": "请输入模型类型", "inputModelPurpose": "请输入模型用途", "inputModelName": "请输入模型名称", "selectModelPurpose": "选择模型用途"}, "login": {"title": "管理后台登录", "description": "请输入您的用户名和密码登录系统", "usernameLabel": "用户名", "usernamePlaceholder": "输入用户名", "passwordLabel": "密码", "passwordPlaceholder": "输入密码", "loggingIn": "登录中...", "login": "登录", "loginFailed": "登录失败，请重试", "verifying": "正在验证身份...", "usernameMinLength": "用户名至少需要2个字符"}, "password": {"changeSuccess": "密码修改成功", "changeFail": "修改密码失败", "changeTitle": "修改超级管理员密码", "changeDescription": "请输入当前密码和新密码以完成修改", "newPassword": "新密码", "newPasswordPlaceholder": "输入新密码", "confirmNewPassword": "确认新密码", "confirmNewPasswordPlaceholder": "再次输入新密码", "mismatch": "新密码与确认密码不匹配", "minLength": "新密码至少需要6个字符", "confirmMinLength": "确认密码至少需要6个字符", "confirmChange": "确认修改"}, "form": {"required": "必填", "emptyTeamName": "团队名称不能为空", "emptyAdmin": "请至少选择一个团队管理员", "emptyModel": "请选择适当的模型", "emptyLoginName": "管理端登录用户名不能为空", "emptyLoginPassword": "管理端登录密码不能为空", "emptySiteName": "站点名称不能为空", "enterTeamName": "请输入团队名称", "inputTeamName": "输入团队名称", "selectAdminRequired": "请至少选择一个团队管理员"}, "site": {"title": "{name} - 从知识和数据中快速获取洞察", "description": "{name}是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。", "configUpdateSuccess": "站点配置更新成功", "configUpdateFail": "站点配置更新失败", "fileUploadSuccess": "文件上传成功", "fileUploadFail": "文件上传失败", "initLoginName": "请初始化管理端登录用户名", "initLoginPassword": "请初始化管理端登录密码", "inputSiteName": "请输入站点名称"}, "sidebar": {"user": "账户管理", "team": "团队管理", "model": "模型配置"}, "misc": {"addUser": "添加用户", "edit": "编辑", "delete": "删除", "view": "查看", "confirmDelete": "确认删除", "unassigned": "未指定"}}