"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";

import type { CreateUser, User, UserList } from "../page";

export function UserForm({
  user,
  onSave,
  isCreating,
  isLoading,
}: {
  user?: Partial<UserList>;
  onSave: (user: User) => void;
  isCreating: boolean;
  isLoading: boolean;
}) {
  const t = useTranslations();

  // 创建用户的表单验证模式（密码必填）
  const createUserSchema = z.object({
    login_name: z.string().min(1, t("users.accountNameRequired")),
    password: z.string().min(6, t("users.passwordRequired")),
  });

  // 编辑用户的表单验证模式（密码可选）
  const updateUserSchema = z.object({
    login_name: z.string().min(1, t("users.accountNameRequired")),
    password: z.string().optional(),
  });

  // 定义表单值类型
  type CreateFormValues = z.infer<typeof createUserSchema>;
  type UpdateFormValues = z.infer<typeof updateUserSchema>;

  // 根据是否为创建模式选择不同的验证模式
  const schema = isCreating ? createUserSchema : updateUserSchema;

  // 初始化表单
  const form = useForm<any>({
    resolver: zodResolver(schema),
    defaultValues: user
      ? { ...user, password: undefined }
      : {
          login_name: "",
          password: "",
        },
    // 当模式切换时重新验证表单
    mode: "onChange",
  });

  // 保存用户
  const handleSubmit = isCreating
    ? (values: CreateFormValues) => {
        // 创建用户 - 不需要 user_id，但密码必填
        onSave({
          ...values,
        } as CreateUser);
      }
    : (values: UpdateFormValues) => {
        // 编辑用户 - 需要 user_id，密码可选
        // 如果密码为空，则不包含在提交的数据中
        const updateData: any = {
          login_name: values.login_name,
        };

        // 只有当密码不为空时，才包含密码字段
        if (values.password && values.password.trim() !== "") {
          updateData.password = values.password;
        }

        onSave(updateData as User);
      };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="login_name"
          rules={{ required: t("users.enterAccountName") }}
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("users.accountName")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={t("users.inputAccountName")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          rules={isCreating ? { required: t("users.enterAccountName") } : undefined}
          render={({ field }) => (
            <FormItem>
              {isCreating ? (
                <RequiredFormLabel>{t("common.password")}</RequiredFormLabel>
              ) : (
                <FormLabel>{t("users.modifyPassword")}</FormLabel>
              )}
              <FormControl>
                <Input
                  type="password"
                  placeholder={
                    isCreating ? t("users.inputPassword") : t("users.leaveEmptyForNoChange")
                  }
                  {...field}
                />
              </FormControl>
              <FormMessage />
              {!isCreating && (
                <p className="text-muted-foreground text-xs">{t("users.noChangePasswordHint")}</p>
              )}
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading}>
            {isLoading && (
              <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            )}
            {isCreating ? t("users.createAccount") : t("users.saveChanges")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
