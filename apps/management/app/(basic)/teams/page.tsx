"use client";

import { formatDateTime } from "@/lib/utils";
import {
  TeamResponse,
  useCreateTeam,
  useDeleteTeam,
  useTeams,
  useUpdateTeam,
} from "@/service/team-service";
import { useQueryUsers } from "@/service/user-service";
import { Button } from "@ragtop-web/ui/components/button";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { type ColumnDef } from "@ragtop-web/ui/components/data-table";
import { IconButton } from "@ragtop-web/ui/components/icon-button";
import { PaginatedTable } from "@ragtop-web/ui/components/paginated-table";
import { Tooltip, TooltipContent, TooltipTrigger } from "@ragtop-web/ui/components/tooltip";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { usePagination } from "@ragtop-web/ui/hooks/use-pagination";
import { Pencil, PlusIcon, Trash2, Users } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { TeamForm } from "./components/team-form";
import { TeamMembersList } from "./components/team-members-list";

// 用户数据结构
export interface User {
  id: string;
  login_name: string;
  nick?: string;
  create_time?: string;
  roles?: Array<{
    id: string;
    name: string;
  }>;
}

// 团队数据结构（用于表单）
export interface Team {
  id: string;
  name: string;
  admin_user_ids: string[]; // 多选管理员
  members: string[]; // 成员ID列表
}

export default function TeamsPage() {
  const t = useTranslations();
  // 分页状态
  const { toast } = useToast();
  const { pageNumber, pageSize, handlePageChange, handlePageSizeChange } = usePagination({
    initialPageSize: 10,
  });

  // 获取团队列表
  const { data, isLoading, isFetching } = useTeams(pageNumber, pageSize);

  // 加载状态
  const isTableLoading = isLoading || isFetching;

  // 创建、更新和删除团队的mutation
  const createTeamMutation = useCreateTeam();
  const updateTeamMutation = useUpdateTeam();
  const deleteTeamMutation = useDeleteTeam();

  // UI状态
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<TeamResponse | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [teamToDelete, setTeamToDelete] = useState<TeamResponse | null>(null);
  const [viewMode, setViewMode] = useState<"edit" | "details">("edit");

  // 处理打开团队详情抽屉
  const handleEditTeam = (team: TeamResponse) => {
    setSelectedTeam(team);
    setIsCreating(false);
    setViewMode("edit");
    setIsDrawerOpen(true);
  };

  // 处理查看团队详情
  const handleViewTeamDetails = (team: TeamResponse) => {
    setSelectedTeam(team);
    setViewMode("details");
    setIsDrawerOpen(true);
  };

  // 处理打开创建团队抽屉
  const handleCreateTeam = () => {
    setSelectedTeam(null);
    setIsCreating(true);
    setViewMode("edit");
    setIsDrawerOpen(true);
  };

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  // 处理保存团队
  const handleSaveTeam = (formData: { name: string; admin_user_ids: string[] }) => {
    if (isCreating) {
      // 创建团队
      createTeamMutation.mutate(formData, {
        onSuccess: () => {
          setSelectedTeam(null);
          setIsDrawerOpen(false);
        },
        onError: (error) => {
          console.error(error);
          toast({
            title: t("teams.create.fail.tips"),
            description: error.message,
            variant: "destructive",
          });
        },
      });
    } else {
      // 编辑团队
      const updateData = {
        name: formData.name,
        team_id: selectedTeam?.id || "",
        admin_user_ids: formData.admin_user_ids,
      };

      updateTeamMutation.mutate(updateData, {
        onSuccess: () => {
          setSelectedTeam(null);
          setIsDrawerOpen(false);
        },
        onError: (error) => {
          console.error(error);
          toast({
            title: t("teams.update.fail.title"),
            description: error.message,
            variant: "destructive",
          });
        },
      });
    }
  };

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (team: TeamResponse) => {
    setTeamToDelete(team);
    setIsDeleteDialogOpen(true);
  };

  // 处理删除团队
  const handleDeleteTeam = () => {
    deleteTeamMutation.mutate(
      { team_id: teamToDelete?.id || "" },
      {
        onSuccess: () => {
          setIsDeleteDialogOpen(false);
          setTeamToDelete(null);
        },
        onError: (error) => {
          console.error(error);
          toast({
            title: t("teams.delete.fail.title"),
            description: error.message,
            variant: "destructive",
          });
        },
      }
    );
  };

  const columns: ColumnDef<TeamResponse>[] = [
    {
      accessorKey: "name",
      header: t("teams.name"),
    },
    {
      accessorKey: "admin_user_ids",
      header: t("teams.admin"),
      cell: ({ row }) => {
        const { admins } = row.original;

        if (!admins || admins.length === 0) {
          return t("teams.unassigned");
        }

        // 显示所有管理员，用逗号分隔
        const adminNames = admins.map((admin) => admin?.login_name).filter(Boolean);

        if (adminNames.length === 0) {
          return t("teams.unassigned");
        }

        const fullText = adminNames.join(", ");
        const shouldShowTooltip = fullText.length > 30; // 当字符串长度超过30时显示tooltip

        const content = <span className="max-w-[200px] truncate">{fullText}</span>;

        if (shouldShowTooltip) {
          return (
            <div className="flex items-center gap-1">
              <Tooltip>
                <TooltipTrigger asChild>{content}</TooltipTrigger>
                <TooltipContent side="top" className="max-w-[300px]">
                  <div className="text-xs">
                    {adminNames.map((name, index) => (
                      <div key={index}>{name}</div>
                    ))}
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          );
        }

        return <div className="flex items-center gap-1">{content}</div>;
      },
    },
    {
      accessorKey: "member_count",
      header: t("teams.memberCount"),
      cell: ({ row }) => row.original.member_count,
    },
    {
      accessorKey: "create_time",
      header: t("teams.createdAt"),
      cell: ({ row }) => {
        const createAt = row.getValue("create_time");

        if (!createAt) {
          return "-";
        }

        try {
          return formatDateTime(createAt as string);
        } catch {
          return "-";
        }
      },
    },
    {
      accessorKey: "action",
      header: () => <div className="w-[100px]"> {t("users.actions")}</div>,
      cell: ({ row }) => {
        const team = row.original;

        return (
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton variant="ghost" size="icon" onClick={() => handleViewTeamDetails(team)}>
                  <Users className="h-4 w-4" />
                  <span className="sr-only">{t("common.view")}</span>
                </IconButton>
              </TooltipTrigger>
              <TooltipContent>{t("common.view")}</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton variant="ghost" size="icon" onClick={() => handleEditTeam(team)}>
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">{t("common.edit")}</span>
                </IconButton>
              </TooltipTrigger>
              <TooltipContent>{t("common.edit")}</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  size="icon"
                  onClick={() => handleOpenDeleteDialog(team)}
                >
                  <Trash2 className="text-destructive h-4 w-4" />
                  <span className="sr-only">{t("common.delete")}</span>
                </IconButton>
              </TooltipTrigger>
              <TooltipContent>{t("common.delete")}</TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // 处理用户查询
  const { mutateAsync: queryUsers } = useQueryUsers();

  return (
    <CustomContainer
      title={t("sidebar.team")}
      action={
        <Button size="sm" onClick={handleCreateTeam}>
          <PlusIcon className="h-4 w-4" />
          {t("teams.createTeam")}
        </Button>
      }
    >
      <div className="rounded-md">
        {isTableLoading ? (
          <div className="flex h-64 items-center justify-center rounded-md border">
            <div className="flex flex-col items-center gap-2">
              <div className="border-primary h-6 w-6 animate-spin rounded-full border-2 border-t-transparent"></div>
              <p className="text-muted-foreground text-sm">{t("common.loading")}</p>
            </div>
          </div>
        ) : (
          <PaginatedTable
            columns={columns}
            data={data}
            isLoading={isTableLoading}
            currentPage={pageNumber}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            showTotal={true}
            showPageSizeSelector={true}
          />
        )}
      </div>

      {/* 团队抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={
          viewMode === "details"
            ? t("teams.details")
            : isCreating
              ? t("teams.createTeam")
              : t("teams.editTeam")
        }
      >
        {viewMode === "details" && selectedTeam ? (
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-muted-foreground text-sm font-medium">{t("teams.name")}</h3>
                  <p className="mt-1">{selectedTeam.name}</p>
                </div>
                <div>
                  <h3 className="text-muted-foreground text-sm font-medium">{t("teams.admin")}</h3>
                  <div className="mt-1">
                    {selectedTeam.admins && selectedTeam.admins.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {selectedTeam.admins.map((admin, index) => (
                          <span key={admin.id} className="inline-flex items-center">
                            {admin.login_name}
                            {index < selectedTeam.admins.length - 1 && (
                              <span className="text-muted-foreground mx-1">•</span>
                            )}
                          </span>
                        ))}
                      </div>
                    ) : (
                      t("teams.unassigned")
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-muted-foreground text-sm font-medium">
                    {t("teams.memberCount")}
                  </h3>
                  <p className="mt-1">{selectedTeam.member_count}</p>
                </div>
                <div>
                  <h3 className="text-muted-foreground text-sm font-medium">
                    {t("teams.createdAt")}
                  </h3>
                  <p className="mt-1">{formatDateTime(selectedTeam.create_time)}</p>
                </div>
              </div>
            </div>
            <TeamMembersList id={selectedTeam.id} />
          </div>
        ) : (
          <TeamForm
            queryUsers={queryUsers}
            teamData={
              isCreating
                ? undefined
                : {
                    id: selectedTeam?.id,
                    name: selectedTeam?.name || "",
                    // 提取所有管理员的ID
                    admin_user_ids: selectedTeam?.admins?.map((admin) => admin.id) || [],
                    // 传递完整的管理员信息
                    admins:
                      selectedTeam?.admins?.map((admin) => ({
                        id: admin.id,
                        login_name: admin.login_name,
                        nick: admin.nick,
                      })) || [],
                  }
            }
            onSave={handleSaveTeam}
            isCreating={isCreating}
          />
        )}
      </CustomDrawer>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteTeam}
        title={t("misc.confirmDelete")}
        description={t("teams.delete.confirm", { name: teamToDelete?.name || "" })}
      />
    </CustomContainer>
  );
}
