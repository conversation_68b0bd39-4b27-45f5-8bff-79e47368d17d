"use client";

import { LLMFactory } from "@/common/model";
import { Model, useConfigurabledModels } from "@/service";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Checkbox } from "@ragtop-web/ui/components/checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@ragtop-web/ui/components/command";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { IApiKeySavingParams } from "../hooks";

// 创建动态表单验证模式的函数
const maxTokensOptions = [
  { label: "32k", value: 32 * 1024 },
  { label: "64k", value: 64 * 1024 },
  { label: "128k", value: 128 * 1024 },
  { label: "256k", value: 256 * 1024 },
  { label: "512k", value: 512 * 1024 },
];

const allowedMaxTokens = maxTokensOptions.map((opt) => opt.value);

const createFormSchema = (
  requiresApiVersion: boolean,
  requiresBaseUrl: boolean,
  requiresApiKey: boolean,
  t: any
) =>
  z.object({
    api_key: requiresApiKey ? z.string().min(1, t("models.apiKeyRequired")) : z.string().optional(),
    base_url: requiresBaseUrl
      ? z.string().min(1, t("models.baseUrlRequired"))
      : z.string().optional(),
    model_purpose: z.string().min(1, t("models.modelPurposeRequired")),
    llm_name: z.string().min(1, t("models.modelNameRequired")),
    api_version: requiresApiVersion
      ? z.string().min(1, t("models.apiVersionRequired"))
      : z.string().optional(),
    max_tokens: z.number().refine((val) => allowedMaxTokens.includes(val), {
      message: t("models.maxTokensRequired"),
    }),
    llm_alias: z.string().min(1, t("models.modelAliasRequired")),
    enable_thinking: z.boolean().optional(),
    model_type: z.string().min(1, t("models.modelTypeRequired")),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface ApiKeyModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: IApiKeySavingParams) => void;
  loading?: boolean;
  initialApiKey?: string;
  llmFactory: Model;
  isEdit?: boolean;
}

const modelsWithVersion = [LLMFactory.AzureOpenAI];

export function ApiKeyModal({
  open,
  onClose,
  onSubmit,
  loading = false,
  initialApiKey = "",
  llmFactory,
  isEdit = false,
}: ApiKeyModalProps) {
  const {
    id,
    name,
    model_purposes,
    model_types = [],
    thinking_spec = {},
    ...restParams
  } = llmFactory;
  const { data: options } = useConfigurabledModels(id);
  const [comboboxOpen, setComboboxOpen] = useState(false);
  const t = useTranslations();

  // 使用 useMemo 优化 tagOptions，避免每次渲染都重新创建
  const tagOptions = useMemo(() => {
    if (model_purposes) {
      return model_purposes.map((item) => ({
        value: item,
        label: item,
      }));
    }

    return [];
  }, [model_purposes]);

  // 检查是否需要API版本字段
  const requiresApiVersion = modelsWithVersion.some((x) => x === name);
  const requiresBaseUrl = [
    LLMFactory.AzureOpenAI,
    LLMFactory.Ollama,
    LLMFactory.Xinference,
    LLMFactory.VLLM,
  ].some((x) => x === name);
  const requiresApiKey = [LLMFactory.OpenAI, LLMFactory.TongYiQianWen, LLMFactory.AzureOpenAI].some(
    (x) => x === name
  );

  // 创建对应的表单验证模式
  const formSchema = createFormSchema(requiresApiVersion, requiresBaseUrl, requiresApiKey, t);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      api_key: initialApiKey,
      base_url: "",
      llm_name: "",
      model_purpose: tagOptions[0]?.value || "",
      api_version: "",
      model_type: "",
      max_tokens: 128 * 1024,
      enable_thinking: thinking_spec?.default_value ?? false,
    },
  });

  useEffect(() => {
    if (open && isEdit) {
      const initialValues = {
        api_key: "********",
        ...restParams,
        max_tokens: Number(restParams.max_tokens),
      };

      form.reset(initialValues);
    }
  }, [open, initialApiKey, isEdit]);

  // 监听模型用途的变化
  const selectedModelPurpose = form.watch("model_purpose");

  // 根据选择的模型用途过滤模型名称选项
  const filteredModelOptions = useMemo(() => {
    if (!options || !selectedModelPurpose) {
      return [];
    }

    return options.filter((option) => option.model_purpose?.toString() === selectedModelPurpose);
  }, [options, selectedModelPurpose]);

  // 当模型用途改变时，清空模型名称的选择
  useEffect(() => {
    if (selectedModelPurpose && !isEdit) {
      form.setValue("llm_name", "");
    }
  }, [selectedModelPurpose, form, isEdit]);

  // 处理表单提交
  const handleSubmit = (values: FormValues) => {
    // 处理api_key的逻辑
    let processedApiKey: string | null;

    if (values.api_key === "********") {
      processedApiKey = null;
    } else if (values.api_key === "" || !values.api_key) {
      // 如果是空，传空字符串
      processedApiKey = "";
    } else {
      // 其他情况返回实际字符串
      processedApiKey = values.api_key;
    }

    const finalValue: IApiKeySavingParams = {
      llm_factory_id: id,
      api_key: processedApiKey,
      base_url: values?.base_url || undefined,
      api_version: values?.api_version || undefined,
      llm_name: values.llm_name,
      model_purpose: values.model_purpose,
      max_tokens: values.max_tokens,
      llm_alias: values.llm_alias,
      enable_thinking: values.enable_thinking,
      model_type: values.model_type,
    };

    if (isEdit) {
      finalValue.id = restParams.id;
      finalValue.llm_id = restParams.llm_id;
    }
    onSubmit(finalValue);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-h-[95vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEdit ? t("models.editConfig") : t("models.addLLM")}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="llm_alias"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("models.inputModelAlias")}</RequiredFormLabel>
                  <FormControl>
                    <FormControl>
                      <Input placeholder={t("models.inputModelAlias")} {...field} />
                    </FormControl>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="model_type"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("models.inputModelType")}</RequiredFormLabel>
                  {isEdit ? (
                    <Input disabled={isEdit} placeholder={t("models.inputModelType")} {...field} />
                  ) : (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder={t("models.selectModelType")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {model_types.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="model_purpose"
              rules={{ required: t("models.inputModelPurpose") }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("models.inputModelPurpose")}</RequiredFormLabel>
                  {isEdit ? (
                    <Input
                      disabled={isEdit}
                      placeholder={t("models.inputModelPurpose")}
                      {...field}
                    />
                  ) : (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full" disabled={isEdit}>
                          <SelectValue placeholder={t("models.selectModelPurpose")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tagOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="llm_name"
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("models.inputModelName")}</RequiredFormLabel>
                  {[
                    LLMFactory.Ollama,
                    LLMFactory.Xinference,
                    LLMFactory.VLLM,
                    LLMFactory.LocalAI,
                  ].includes(name as LLMFactory) ? (
                    <Input disabled={isEdit} placeholder={t("models.inputModelName")} {...field} />
                  ) : (
                    <Popover open={comboboxOpen} onOpenChange={setComboboxOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            disabled={isEdit}
                            aria-expanded={comboboxOpen}
                            className="w-full justify-between"
                          >
                            {field.value
                              ? filteredModelOptions.find(
                                  (option) => option.llm_name === field.value
                                )?.llm_name || field.value
                              : t("models.selectModelPurpose")}
                            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-[var(--radix-popover-trigger-width)] p-0"
                        align="start"
                      >
                        <Command>
                          <CommandInput
                            disabled={isEdit}
                            placeholder={t("models.searchModelName")}
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value);
                            }}
                          />
                          <CommandList>
                            <CommandEmpty>
                              <div className="p-2">
                                <p className="text-muted-foreground text-sm">
                                  {t("models.noResults")}
                                </p>
                                {field.value && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="mt-2 w-full"
                                    onClick={() => {
                                      setComboboxOpen(false);
                                    }}
                                  >
                                    {t("models.useModelName", { modelName: field.value })}
                                  </Button>
                                )}
                              </div>
                            </CommandEmpty>
                            <CommandGroup>
                              {filteredModelOptions.map((option) => (
                                <CommandItem
                                  key={option.llm_name}
                                  value={option.llm_name}
                                  onSelect={(currentValue: string) => {
                                    field.onChange(currentValue);
                                    setComboboxOpen(false);
                                  }}
                                >
                                  {option.llm_name}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="api_key"
              render={({ field }) => (
                <FormItem>
                  {requiresApiKey ? (
                    <RequiredFormLabel>{t("models.apiKeyRequired")}</RequiredFormLabel>
                  ) : (
                    <FormLabel>{t("models.apiKey")}</FormLabel>
                  )}
                  <FormControl>
                    <Input placeholder={t("models.apiKeyPlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="base_url"
              render={({ field }) => (
                <FormItem>
                  {requiresBaseUrl ? (
                    <RequiredFormLabel>{t("models.baseUrlRequired")}</RequiredFormLabel>
                  ) : (
                    <FormLabel>{t("models.baseUrl")}</FormLabel>
                  )}
                  <FormControl>
                    <Input placeholder={t("models.baseUrlPlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {requiresApiVersion && (
              <FormField
                control={form.control}
                name="api_version"
                render={({ field }) => (
                  <FormItem>
                    <RequiredFormLabel>{t("models.apiVersionRequired")}</RequiredFormLabel>
                    <FormControl>
                      <Input placeholder={t("models.apiVersionPlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* max_tokens 下拉选择框 */}
            <FormField
              control={form.control}
              name="max_tokens"
              rules={{ required: t("models.maxTokensRequired") }}
              render={({ field }) => (
                <FormItem>
                  <RequiredFormLabel>{t("models.maxTokensRequired")}</RequiredFormLabel>
                  <Select
                    onValueChange={(val) => field.onChange(Number(val))}
                    value={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t("models.selectMaxTokens")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {maxTokensOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {thinking_spec?.enable_setting && (
              <FormField
                control={form.control}
                name="enable_thinking"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel className="mb-0 cursor-pointer" htmlFor="enable_thinking">
                        {t("models.enableThinking")}
                      </FormLabel>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={loading}>
                {t("common.submit")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
