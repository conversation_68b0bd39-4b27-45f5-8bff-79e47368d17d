"use client";

import { IconMap } from "@/common/model";
import {
  ConfiguredModel,
  ConfiguredModelList,
  Model,
  useDeleteModel,
  useExistingModels,
  useUnconfiguredModels,
} from "@/service/model-service";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";

import { Badge } from "@ragtop-web/ui/components/badge";
import { Button } from "@ragtop-web/ui/components/button";
import { Card } from "@ragtop-web/ui/components/card";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { Tooltip, TooltipContent, TooltipTrigger } from "@ragtop-web/ui/components/tooltip";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { ChevronDown, ChevronRight, Pencil, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useCallback, useState } from "react";
import { ApiKeyModal } from "./components/api-key-modal";
import { useSubmitApiKey } from "./hooks";

// 待配置模型卡片组件
function ModelCard({ model, onConfigure }: { model: Model; onConfigure?: () => void }) {
  const t = useTranslations();
  const icon = IconMap[model.name as keyof typeof IconMap];
  const tags = model.model_purposes.join(", ");

  return (
    <Card className="relative flex flex-col p-4 transition-colors">
      <div className="mb-2 flex items-center">
        <div className="border-border mr-3 flex h-12 w-12 items-center justify-center rounded-md border bg-white">
          {/* 使用图片或SVG图标 */}
          <Image src={icon} alt={model.name} width={32} height={32} />
        </div>
        <div>
          <h3 className="font-semibold">{model.name}</h3>
          <p className="text-muted-foreground text-xs break-words whitespace-normal">{tags}</p>
        </div>
      </div>

      <div className="mt-auto">
        <Button variant="outline" size="sm" className="mt-2 w-full" onClick={onConfigure}>
          {t("models.addLLM")}
        </Button>
      </div>
    </Card>
  );
}

// 已配置模型卡片组件
function ConfigureModelCard({
  model,
  onEdit,
  onDelete,
}: {
  model: ConfiguredModelList;
  onEdit: () => void;
  onDelete: () => void;
}) {
  const t = useTranslations();
  const icon = IconMap[model.llm_factory as keyof typeof IconMap];

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleOpenDeleteDialog = () => {
    setIsDeleteDialogOpen(true);
  };

  // // 判断按钮文本
  // const getButtonText = (modelId: string) => {
  //   // 根据需求中的逻辑判断
  //   if (
  //     isLocalLlmFactory(modelId) ||
  //     // modelId === "volc-engine" ||
  //     // modelId === "tencent-hunyuan" ||
  //     // modelId === "xunfei-spark" ||
  //     // modelId === "baidu-yiyan" ||
  //     // modelId === "fish-audio" ||
  //     // modelId === "tencent-cloud" ||
  //     // modelId === "google-cloud" ||
  //     modelId === "azure-openai"
  //   ) {
  //     return "添加模型";
  //   }
  //   return "API-Key";
  // };

  return (
    <>
      <Card className="relative p-4 transition-colors">
        <div className="flex items-start justify-between">
          <div className="mb-2 flex items-center">
            <div className="border-border mr-3 flex h-12 w-12 items-center justify-center rounded-md border bg-white">
              <Image src={icon} alt={model.llm_factory} width={32} height={32} />
            </div>
            <div>
              <h3 className="font-semibold">{model.llm_alias || model.llm_factory}</h3>
              <p className="text-muted-foreground text-sm">
                {model.llm_name}
                <Badge variant="secondary" className="ml-2">
                  {model.model_purpose}
                </Badge>
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  title={t("models.editConfig")}
                  className="text-muted-foreground hover:text-foreground"
                  onClick={onEdit}
                >
                  <Pencil className="h-4 w-4" />
                  <span className="sr-only">{t("models.editConfig")}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>{t("models.editConfig")}</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  title={t("models.deleteModel")}
                  onClick={handleOpenDeleteDialog}
                >
                  <Trash2 className="text-destructive h-4 w-4" />
                  <span className="sr-only">{t("common.delete")}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>{t("models.deleteModel")}</TooltipContent>
            </Tooltip>
          </div>
        </div>
      </Card>
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={onDelete}
        title={t("misc.confirmDelete")}
        description={t("models.confirmDelete", {
          model: `${model?.llm_factory}-${model?.llm_name}`,
        })}
      />
    </>
  );
}

export default function ModelsPage() {
  const t = useTranslations();
  const { toast } = useToast();
  const [configuredExpanded, setConfiguredExpanded] = useState(true);
  const [unconfiguredExpanded, setUnconfiguredExpanded] = useState(true);
  const [isEdit, setIsEdit] = useState<boolean>(false);

  const { data: configuredModels } = useExistingModels();
  const { data: unconfiguredModels } = useUnconfiguredModels();
  const deleteMutate = useDeleteModel();

  const {
    saveApiKeyLoading,
    initialApiKey,
    llmFactory,
    onApiKeySavingOk,
    apiKeyVisible,
    hideApiKeyModal,
    showApiKeyModal,
  } = useSubmitApiKey();

  // const {
  //  NL2SQLSettinLoading,
  //   onNL2SQLSettingOk,
  //   NL2SQLSettingVisible,
  //   hideNL2SQLSettingModal,
  //   showNL2SQLSettingModal,
  // } = useSubmitNL2SQL()

  const toggleConfigured = () => {
    setConfiguredExpanded(!configuredExpanded);
  };

  const toggleUnconfigured = () => {
    setUnconfiguredExpanded(!unconfiguredExpanded);
  };

  const handleConfigureModel = useCallback(
    (model: Model | ConfiguredModel, isEdit: boolean = false) => {
      if (isEdit) {
        setIsEdit(true);
      } else {
        setIsEdit(false);
      }

      const modelConfig = unconfiguredModels?.find((item) => item.id === model.llm_factory_id);

      // 将 ConfiguredModelList 转换为 Model 类型
      if ("llm_id" in model) {
        const convertedModel: Model & ConfiguredModel = {
          id: model.llm_factory_id || "",
          name: model.llm_name || "",
          logo: "",
          ...model,
          ...modelConfig,
        };

        showApiKeyModal(convertedModel);
      } else {
        // 这是一个未配置的模型
        showApiKeyModal(model);
      }
    },
    [showApiKeyModal]
  );

  const handleDelete = (model: ConfiguredModelList) => {
    deleteMutate.mutate(
      { llm_id: model.llm_id },
      {
        onSuccess: () => {
          toast({
            title: t("models.deleteSuccess"),
          });
        },
        onError: (error) => {
          console.error(error);
          toast({
            title: t("models.deleteFail"),
            description: error.message,
            variant: "destructive",
          });
        },
      }
    );
  };

  return (
    <CustomContainer
      title={t("sidebar.model")}
      // action={
      //   <Button onClick={showNL2SQLSettingModal}>
      //       <Settings className="h-4 w-4" />
      //       nl2sql配置
      //     </Button>
      // }
    >
      {/* 已配置的模型 */}
      <div className="mb-8">
        <button className="text-l mb-4 flex items-center font-semibold" onClick={toggleConfigured}>
          {configuredExpanded ? (
            <ChevronDown className="mr-2" />
          ) : (
            <ChevronRight className="mr-2" />
          )}
          {t("models.configuredModels")}
        </button>

        {configuredExpanded && (
          <div className="grid grid-cols-1 gap-4">
            {configuredModels?.map((model) => (
              <ConfigureModelCard
                key={model.llm_id}
                model={model}
                onEdit={() => handleConfigureModel(model, true)}
                onDelete={() => handleDelete(model)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 待配置的模型 */}
      <div>
        <button
          className="text-l mb-4 flex items-center font-semibold"
          onClick={toggleUnconfigured}
        >
          {unconfiguredExpanded ? (
            <ChevronDown className="mr-2" />
          ) : (
            <ChevronRight className="mr-2" />
          )}
          {t("models.unconfiguredModels")}
        </button>

        {unconfiguredExpanded && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {unconfiguredModels?.map((model) => (
              <ModelCard
                key={model.name}
                model={model}
                onConfigure={() => handleConfigureModel(model)}
              />
            ))}
          </div>
        )}
      </div>

      {/* API Key 模态框 */}
      {apiKeyVisible && (
        <ApiKeyModal
          open={apiKeyVisible}
          onClose={hideApiKeyModal}
          onSubmit={onApiKeySavingOk}
          loading={saveApiKeyLoading}
          initialApiKey={initialApiKey}
          llmFactory={llmFactory}
          isEdit={isEdit}
        />
      )}

      {/* <Nl2SQLDialog
      open={NL2SQLSettingVisible}
        onClose={hideNL2SQLSettingModal}
        onSubmit={onNL2SQLSettingOk}
        loading={NL2SQLSettinLoading}
        /> */}
    </CustomContainer>
  );
}
