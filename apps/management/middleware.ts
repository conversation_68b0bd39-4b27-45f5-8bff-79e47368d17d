import { NextRequest, NextResponse } from "next/server";

function getRealOrigin(request: NextRequest): string {
  const proto = request.headers.get("x-forwarded-proto") || "http";
  const host =
    request.headers.get("x-forwarded-host") || request.headers.get("host");
  return `${proto}://${host}`;
}

function getBaseUrl(req: NextRequest, apiBaseUrl: string | undefined) {
  if (apiBaseUrl) {
    return apiBaseUrl;
  }

  return getRealOrigin(req);
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // ✅ 健康检查路径，直接放行
  if (pathname === "/") {
    return NextResponse.next();
  }

  // 跳过静态资源和后端 API 路由
  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/frontend-api/") ||
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/static/") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  // 取环境变量
  const apiUrl = process.env.MANAGEMENT_INNER_API_URL || "";
  const apiPrefix = process.env.API_PREFIX || "/api/v1/portal-ragtop-admin";
  const baseUrl = getBaseUrl(request, apiUrl);

  try {
    const checkUrl = `${baseUrl}${apiPrefix}/site/check-init-status`;

    // 调用后端接口
    const aResponse = await fetch(checkUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!aResponse.ok) {
      // 如果返回 404 或其他错误，直接返回静态 HTML
      return new NextResponse(
        `
          <html>
            <head><title>error</title></head>
            <body>
              <h1>error</h1>
              <p>status: ${aResponse.status}</p>
              <p>message: ${aResponse.statusText}</p>
              <p>checkUrl: ${checkUrl}</p>
            </body>
          </html>
          `,
        {
          status: aResponse.status,
          headers: { "Content-Type": "text/html" },
        }
      );
    }
    // throw new Error(`check-init-status接口请求失败: ${aResponse.status}`);

    const aResult = await aResponse.json();

    // 判断结果：初始化没完成，跳到 /initial
    if (!aResult || aResult.data !== true) {
      if (pathname !== "/initial") {
        return NextResponse.redirect(new URL("/initial", request.url));
      }
      return NextResponse.next();
    }

    // 初始化已完成，访问 /initial 页面就跳转到首页
    if (pathname === "/initial") {
      return NextResponse.redirect(new URL("/", request.url));
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    console.error("baseurl", baseUrl);
    // 接口异常时跳转到 /login
    if (pathname !== "/login") {
      return NextResponse.redirect(new URL("/login", request.url));
    }
    return NextResponse.next();
  }
}

// 配置：只拦截非 /api、非静态资源的请求
export const config = {
  matcher: ["/((?!api|frontend-api|_next/static|_next/image|favicon.ico).*)"],
};
