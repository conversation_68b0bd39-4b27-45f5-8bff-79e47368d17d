"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AgentDetailsParams, <PERSON><PERSON>, <PERSON> } from "@/service/agent-service";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";

import { Badge } from "@ragtop-web/ui/components/badge";
import { Button } from "@ragtop-web/ui/components/button";

import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ragtop-web/ui/components/tabs";

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select";
import { Slider } from "@ragtop-web/ui/components/slider";
import { Textarea } from "@ragtop-web/ui/components/textarea";

import { ConfiguredModelList, useExistingModels } from "@/service/model-service";
import { TeamType } from "@/service/team-service";
import { currentTeamAtom } from "@/store/team-store";
import { Label } from "@ragtop-web/ui/components/label";
import { Separator } from "@ragtop-web/ui/components/separator";
import { useAtomValue } from "jotai";
import { isEmpty } from "lodash-es";
import { Loader2 } from "lucide-react";
import { useConfig } from "../config-provider-client";
import { DatasetSelector } from "./dataset-selector";
import { FilesetSelector } from "./fileset-selector";
import { KnowledgeBaseSelector } from "./knowledge-base-selector";

// 支持的语言选项
const LANGUAGE_OPTIONS = [
  { value: "English", label: "English" },
  { value: "Chinese", label: "中文" },
];

interface AgentDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: AgentCreateParams) => void;
  initialData?: AgentDetailsParams;
}

/**
 * Agent 对话框组件，包含三个标签页：助理设置、提示引擎、模型设置
 */
export function AgentDialog({ open, onClose, onSubmit, initialData }: AgentDialogProps) {
  const { data: existingModels } = useExistingModels(true);
  const notShowNL2Code = useConfig().SHOW_NL2CODE === "false";
  const [chatModels, setChatModels] = useState<ConfiguredModelList[]>([]);
  const [nlTab, setNlTab] = useState("dbsets");
  const t = useTranslations("agent");

  const currentTeam = useAtomValue(currentTeamAtom);

  const isPersonalTeam = currentTeam?.type === TeamType.Personal;
  // Tab 状态管理
  const [activeTab, setActiveTab] = useState("assistant-settings");
  const previousFlexibilityRef = useRef<string | undefined>(initialData?.settings?.llm_model);
  const [isLoading, setIsLoading] = useState(false);

  // 默认表单数据
  const defaultFormData: AgentCreateParams = {
    name: "",
    description: "",
    abnormal_message: "",
    hello_message: t("helloMessage"),
    type: Type.Rag, // 默认资源类型为"知识库"
    history_num: 1,
    link_resources: [], // 默认资源为空
    scope: Scope.Private, // 默认Agent权限为"个人"
    prompt: "",
    summary_prompt: "",
    settings: {
      keyword_weight: 0.5,
      llm_model: undefined,
      similarity_threshold: 0.5,
      temperature: 0.5,
      top_n: 64,
      top_p: 0.5,
    },
    language_settings: {
      cross_languages: [],
    },
  };

  const defaultValue = initialData
    ? {
        ...initialData,
        id: undefined,
        link_resources: initialData?.link_resources?.map((resource: any) => resource.res_id),
        settings: {
          ...initialData.settings,
          llm_model: initialData?.settings?.llm_model,
          llm_model_name: undefined, // 详情会有这个字段，传的时候不需要
        },
      }
    : defaultFormData;

  const form = useForm<AgentCreateParams>({
    defaultValues: defaultValue,
    shouldUnregister: false,
  });

  const resourceType = form.watch("type");
  const llmModel = form.watch("settings.llm_model");

  const handleClose = () => {
    form.reset(defaultValue);
    onClose();
  };
  // 检查每个 Tab 是否有错误
  const getTabErrors = () => {
    const { errors } = form.formState;

    return {
      assistantSettings: !!(
        errors.name ||
        errors.scope ||
        errors.language_settings?.cross_languages
      ),
      promptEngine: !!(
        errors.settings?.similarity_threshold ||
        errors.settings?.keyword_weight ||
        errors.settings?.top_n
      ),
      modelSettings: !!(
        errors.settings?.llm_model ||
        errors.settings?.temperature ||
        errors.settings?.top_p
      ),
    };
  };

  const tabErrors = getTabErrors();

  // 处理表单校验失败
  const handleInvalid = (errors: any) => {
    // 检查哪个 Tab 有错误，并切换到第一个有错误的 Tab
    if (
      errors.type ||
      errors.link_resources ||
      errors.name ||
      errors.scope ||
      errors.language_settings?.cross_languages
    ) {
      setActiveTab("assistant-settings");
    } else if (
      errors.settings?.similarity_threshold ||
      errors.settings?.keyword_weight ||
      errors.settings?.top_n
    ) {
      setActiveTab("prompt-engine");
    } else if (
      errors.settings?.llm_model ||
      errors.settings?.temperature ||
      errors.settings?.top_p
    ) {
      setActiveTab("model-settings");
    }
  };

  const changeNlTabs = (value: string, reset = false) => {
    setNlTab(value);
    if (value === "dbsets") {
      setChatModels(existingModels?.filter((model) => model.model_purpose === "NL2SQL") || []);
      form.setValue("type", Type.NLToSQL);
      if (reset) {
        form.setValue("settings.llm_model", undefined);
        form.setValue("link_resources", []);
      }
    }
    if (value === "filesets") {
      setChatModels(existingModels?.filter((model) => model.model_purpose === "NL2PYTHON") || []);
      form.setValue("type", Type.NLToCode);
      if (reset) {
        form.setValue("settings.llm_model", undefined);
        form.setValue("link_resources", []);
      }
    }
  };

  // 处理表单提交成功
  const handleSubmit = async (data: AgentCreateParams) => {
    if (isLoading) {
      return;
    }
    setIsLoading(true);
    try {
      const finalData =
        resourceType === Type.Rag
          ? {
              ...data,
            }
          : {
              ...data,
              settings: {
                llm_model: data.settings?.llm_model,
                temperature: data.settings?.temperature,
                top_p: data.settings?.top_p,
              },
            };

      await onSubmit(finalData);
      handleClose();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // 只有当flexibility发生变化时才更新temperature和top_p
    if (llmModel && previousFlexibilityRef.current !== llmModel) {
      const model = chatModels?.find((model) => model.llm_id === llmModel);

      if (model) {
        const { temperature, top_p } = model;

        form.setValue("settings.temperature", temperature !== undefined ? temperature : 0.5);
        form.setValue("settings.top_p", top_p !== undefined ? top_p : 0.5);
      }
      previousFlexibilityRef.current = llmModel;
    }
  }, [llmModel, form]);

  // 当对话框打开时，重置到第一个 Tab
  useEffect(() => {
    if (open) {
      setActiveTab("assistant-settings");
    }
  }, [open]);

  useEffect(() => {
    if (initialData) {
      if (initialData.type === Type.NLToSQL) {
        changeNlTabs("dbsets", false);
      } else if (initialData.type === Type.NLToCode) {
        changeNlTabs("filesets", false);
      } else {
        setChatModels(existingModels?.filter((model) => model.model_purpose === "CHAT") || []);
      }
    } else {
      setChatModels(existingModels?.filter((model) => model.model_purpose === "CHAT") || []);
    }
  }, [initialData, existingModels]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="flex h-[80vh] max-w-3xl flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{t("dialog.title")}</DialogTitle>
          <p className="text-muted-foreground text-sm">{t("dialog.description")}</p>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit, handleInvalid)}
              className="flex h-full flex-col"
            >
              <div className="no-scrollbar flex-1 space-y-4 overflow-y-auto">
                <FormField
                  control={form.control}
                  name="type"
                  rules={{ required: t("form.validation.resourceTypeRequired") }}
                  render={({ field }) => (
                    <FormItem>
                      <RequiredFormLabel>{t("form.resourceType")}</RequiredFormLabel>
                      <FormControl>
                        <div className="flex items-center space-x-6">
                          <div className="flex items-center space-x-2">
                            <input
                              disabled={initialData ? true : false}
                              type="radio"
                              id={Type.Rag}
                              value={Type.Rag}
                              checked={field.value === Type.Rag}
                              onChange={() => {
                                field.onChange(Type.Rag);
                                setChatModels(
                                  existingModels?.filter(
                                    (model) => model.model_purpose === "CHAT"
                                  ) || []
                                );
                                form.setValue("link_resources", []);
                                form.setValue("summary_prompt", undefined);
                                form.setValue("history_num", 1);
                              }}
                              className="text-primary border-input focus:ring-primary h-4 w-4"
                            />
                            <Label htmlFor={Type.Rag} className="cursor-pointer">
                              {t("form.knowledgeBase")}
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              disabled={initialData ? true : false}
                              id={Type.NLToSQL}
                              value={Type.NLToSQL}
                              checked={
                                field.value === Type.NLToSQL || field.value === Type.NLToCode
                              }
                              onChange={() => {
                                field.onChange(Type.NLToSQL);
                                setChatModels(
                                  existingModels?.filter(
                                    (model) => model.model_purpose === "NL2SQL"
                                  ) || []
                                );
                                form.setValue("link_resources", []);
                                form.setValue("history_num", 0);
                              }}
                              className="text-primary border-input focus:ring-primary h-4 w-4"
                            />
                            <Label htmlFor={Type.NLToSQL} className="cursor-pointer">
                              {t("form.datasets")}
                            </Label>
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="link_resources"
                  rules={{ required: t("form.validation.resourceRequired") }}
                  render={({ field }) => {
                    if (resourceType === Type.Rag) {
                      // 知识库多选
                      return (
                        <FormItem>
                          <RequiredFormLabel>{t("form.knowledgeBase")}</RequiredFormLabel>
                          <KnowledgeBaseSelector
                            selectedIds={field.value || []}
                            onSelectionChange={field.onChange}
                            maxHeight="200px"
                          />
                          <FormMessage />
                        </FormItem>
                      );
                    } else {
                      // 数据库数据集单选，文件数据集多选
                      return (
                        <FormItem>
                          <RequiredFormLabel>{t("form.datasets")}</RequiredFormLabel>
                          <Tabs
                            className="w-full"
                            value={nlTab}
                            onValueChange={(value) => {
                              changeNlTabs(value, true);
                            }}
                          >
                            {!notShowNL2Code && (
                              <TabsList className={`grid w-full grid-cols-2`}>
                                <TabsTrigger disabled={initialData ? true : false} value="dbsets">
                                  {t("form.database")}
                                </TabsTrigger>
                                <TabsTrigger disabled={initialData ? true : false} value="filesets">
                                  {t("form.file")}
                                </TabsTrigger>
                              </TabsList>
                            )}
                            <TabsContent value="dbsets" className="space-y-4">
                              {nlTab === "dbsets" && (
                                <DatasetSelector
                                  selectedIds={field.value || []}
                                  onSelectionChange={field.onChange}
                                  maxHeight="200px"
                                />
                              )}
                            </TabsContent>
                            <TabsContent value="filesets" className="space-y-4">
                              {nlTab === "filesets" && (
                                <FilesetSelector
                                  selectedIds={field.value || []}
                                  onSelectionChange={field.onChange}
                                  maxHeight="200px"
                                />
                              )}
                            </TabsContent>
                          </Tabs>
                          <FormMessage />
                        </FormItem>
                      );
                    }
                  }}
                />

                <Separator />
                <div className="w-full">
                  {/* Tab 导航 */}
                  <div className={`bg-muted mb-4 grid w-full grid-cols-3 rounded-md p-1`}>
                    <button
                      type="button"
                      onClick={() => setActiveTab("assistant-settings")}
                      className={`relative rounded-sm px-3 py-1.5 text-sm font-medium transition-all ${
                        activeTab === "assistant-settings"
                          ? "bg-background text-foreground shadow-sm"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      {t("tabs.basicSettings")}
                      {tabErrors.assistantSettings && (
                        <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500"></span>
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={() => setActiveTab("prompt-engine")}
                      className={`relative rounded-sm px-3 py-1.5 text-sm font-medium transition-all ${
                        activeTab === "prompt-engine"
                          ? "bg-background text-foreground shadow-sm"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      {t("tabs.promptEngine")}
                      {tabErrors.promptEngine && (
                        <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500"></span>
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={() => setActiveTab("model-settings")}
                      className={`relative rounded-sm px-3 py-1.5 text-sm font-medium transition-all ${
                        activeTab === "model-settings"
                          ? "bg-background text-foreground shadow-sm"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      {t("tabs.modelSettings")}
                      {tabErrors.modelSettings && (
                        <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500"></span>
                      )}
                    </button>
                  </div>

                  {/* 助理设置标签页 */}
                  <div
                    className={`space-y-4 px-1 ${activeTab === "assistant-settings" ? "block" : "hidden"}`}
                  >
                    <FormField
                      control={form.control}
                      name="name"
                      rules={{ required: t("form.validation.nameRequired") }}
                      render={({ field }) => (
                        <FormItem>
                          <RequiredFormLabel>{t("form.name")}</RequiredFormLabel>
                          <FormControl>
                            <Input placeholder={t("form.namePlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("form.description")}</FormLabel>
                          <FormControl>
                            <Textarea placeholder={t("form.descriptionPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="abnormal_message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("form.emptyReply")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("form.emptyReplyPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="hello_message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("form.greeting")}</FormLabel>
                          <FormControl>
                            <Textarea placeholder={t("form.greetingPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="history_num"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center justify-between">
                            <FormLabel>{t("form.historyContext")}</FormLabel>
                            <span className="text-sm">{field.value}</span>
                          </div>
                          <FormControl>
                            <Slider
                              value={[field.value]}
                              min={0}
                              max={3}
                              step={1}
                              onValueChange={(value) => field.onChange(value[0])}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="language_settings.cross_languages"
                      rules={{
                        required: t("form.validation.languageRequired"),
                        validate: (value) =>
                          (value && value.length > 0) || t("form.validation.languageRequired"),
                      }}
                      render={({ field }) => (
                        <FormItem>
                          <RequiredFormLabel>{t("form.supportedLanguages")}</RequiredFormLabel>
                          <FormControl>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  className="w-full justify-between"
                                >
                                  {field.value && field.value.length > 0
                                    ? field.value
                                        .map(
                                          (lang: string) =>
                                            LANGUAGE_OPTIONS.find((option) => option.value === lang)
                                              ?.label
                                        )
                                        .filter(Boolean)
                                        .join(", ")
                                    : t("form.supportedLanguagesPlaceholder")}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                                <div className="p-2">
                                  {LANGUAGE_OPTIONS.map((option) => (
                                    <div
                                      key={option.value}
                                      className="hover:bg-accent hover:text-accent-foreground flex cursor-pointer items-center space-x-2 rounded-sm p-2"
                                      onClick={() => {
                                        const currentValues = field.value || [];
                                        const isSelected = currentValues.includes(option.value);

                                        if (isSelected) {
                                          field.onChange(
                                            currentValues.filter(
                                              (value: string) => value !== option.value
                                            )
                                          );
                                        } else {
                                          field.onChange([...currentValues, option.value]);
                                        }
                                      }}
                                    >
                                      <input
                                        type="checkbox"
                                        checked={field.value?.includes(option.value) || false}
                                        readOnly
                                        className="text-primary border-input focus:ring-primary h-4 w-4"
                                      />
                                      <span className="text-sm">{option.label}</span>
                                    </div>
                                  ))}
                                </div>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormDescription>
                            {t("form.supportedLanguagesDescription")}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {!isPersonalTeam && (
                      <FormField
                        control={form.control}
                        name="scope"
                        rules={{ required: t("form.validation.permissionRequired") }}
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <RequiredFormLabel>{t("form.agentPermission")}</RequiredFormLabel>
                            <FormControl>
                              <div className="flex items-center space-x-6">
                                <div className="flex items-center space-x-2">
                                  <input
                                    type="radio"
                                    id="PRIVATE"
                                    value="PRIVATE"
                                    checked={field.value === "PRIVATE"}
                                    onChange={() => field.onChange("PRIVATE")}
                                    className="text-primary border-input focus:ring-primary h-4 w-4"
                                  />
                                  <Label htmlFor="PRIVATE" className="cursor-pointer">
                                    {t("form.personal")}
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <input
                                    type="radio"
                                    id="TEAM_PUBLIC"
                                    value="TEAM_PUBLIC"
                                    disabled={isPersonalTeam}
                                    checked={field.value === "TEAM_PUBLIC"}
                                    onChange={() => field.onChange("TEAM_PUBLIC")}
                                    className="text-primary border-input focus:ring-primary h-4 w-4"
                                  />
                                  <Label htmlFor="TEAM_PUBLIC" className="cursor-pointer">
                                    {t("form.team")}
                                  </Label>
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  {/* 提示引擎标签页 */}
                  <div
                    className={`space-y-4 ${activeTab === "prompt-engine" ? "block" : "hidden"}`}
                  >
                    <FormField
                      control={form.control}
                      name="prompt"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("form.systemPrompt")}</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t("form.systemPromptPlaceholder")}
                              className="min-h-32"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {resourceType !== Type.Rag && (
                      <FormField
                        control={form.control}
                        name="summary_prompt"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("form.summaryPrompt")}</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder={t("form.summaryPromptPlaceholder")}
                                className="min-h-32"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    {resourceType === Type.Rag && (
                      <>
                        <FormField
                          control={form.control}
                          name="settings.similarity_threshold"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex items-center justify-between">
                                <FormLabel>{t("form.similarityThreshold")}</FormLabel>
                                <span className="text-sm">{field.value}</span>
                              </div>
                              <FormControl>
                                <Slider
                                  value={[field.value || 0]}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  onValueChange={(value) => field.onChange(value[0])}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="settings.keyword_weight"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex items-center justify-between">
                                <FormLabel>{t("form.keywordWeight")}</FormLabel>
                                <span className="text-sm">{field.value}</span>
                              </div>
                              <FormControl>
                                <Slider
                                  value={[field.value || 0]}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  onValueChange={(value) => field.onChange(value[0])}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="settings.top_n"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex items-center justify-between">
                                <FormLabel>{t("form.topN")}</FormLabel>
                                <span className="text-sm">{field.value}</span>
                              </div>
                              <FormControl>
                                <Slider
                                  value={[field.value || 1]}
                                  min={1}
                                  max={128}
                                  step={1}
                                  onValueChange={(value) => field.onChange(value[0])}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                  </div>

                  {/* 模型设置标签页 */}

                  <div
                    className={`space-y-4 ${activeTab === "model-settings" ? "block" : "hidden"}`}
                  >
                    <FormField
                      control={form.control}
                      name="settings.llm_model"
                      rules={{ required: t("form.validation.modelRequired") }}
                      render={({ field }) => (
                        <FormItem>
                          <RequiredFormLabel>{t("form.model")}</RequiredFormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-[462px]">
                                <SelectValue placeholder={t("form.modelPlaceholder")} />
                              </SelectTrigger>
                            </FormControl>

                            <SelectContent>
                              {chatModels?.map((model) => (
                                <SelectItem key={model.llm_id} value={model.llm_id}>
                                  <div className="flex items-center gap-2">
                                    <span>{model.llm_alias || model.llm_name}</span>
                                    {model.llm_alias && model.model_type && (
                                      <Badge variant="secondary" className="text-xs">
                                        {model.model_type}
                                      </Badge>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                          {isEmpty(chatModels) && (
                            <FormDescription className="text-destructive">
                              {t("form.modelNotConfiguredMessage", {
                                modelType:
                                  resourceType === Type.Rag
                                    ? t("form.chatModel")
                                    : t("form.datasetChatModel"),
                              })}
                            </FormDescription>
                          )}
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="settings.temperature"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center justify-between">
                            <FormLabel>{t("form.temperature")}</FormLabel>
                            <span className="text-sm">{field.value || 0}</span>
                          </div>
                          <FormControl>
                            <Slider
                              value={[field.value || 0]}
                              min={0}
                              max={1}
                              step={0.01}
                              onValueChange={(value) => field.onChange(value[0])}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="settings.top_p"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center justify-between">
                            <FormLabel>{t("form.topP")}</FormLabel>
                            <span className="text-sm">{field.value || 0}</span>
                          </div>
                          <FormControl>
                            <Slider
                              value={[field.value || 0]}
                              min={0}
                              max={1}
                              step={0.01}
                              onValueChange={(value) => field.onChange(value[0])}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleClose}>
                  {t("dialog.cancel")}
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && (
                    <span className="mr-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </span>
                  )}
                  {t("dialog.confirm")}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
