/**
 * 测试 i18n-ally 插件功能的组件
 * 用于验证翻译同步是否正常工作
 */

'use client';

import { useTranslations } from 'next-intl';

export function TestI18nComponent() {
  const t = useTranslations('test');
  
  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-lg font-semibold mb-4">
        {t('title')}
      </h2>
      
      <div className="space-y-2">
        <p>{t('description')}</p>
        <p>{t('instructions')}</p>
        
        <div className="flex gap-2">
          <button className="px-4 py-2 bg-blue-500 text-white rounded">
            {t('button.save')}
          </button>
          <button className="px-4 py-2 bg-gray-500 text-white rounded">
            {t('button.cancel')}
          </button>
        </div>
        
        <div className="mt-4 p-2 bg-yellow-100 rounded">
          <p className="text-sm text-yellow-800">
            {t('note')}
          </p>
        </div>
      </div>
    </div>
  );
}

export default TestI18nComponent;
