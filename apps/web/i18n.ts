import { getRequestConfig } from "next-intl/server";

export default getRequestConfig(async () => {
  // 从请求头或默认设置获取语言
  // const headersList = await headers();
  // const acceptLanguage = headersList.get("accept-language") || "";

  // 根据浏览器语言偏好选择语言，默认为中文
  const locale = "en";

  // 也可以从 cookie 或其他来源获取用户设置的语言
  // const locale = cookies().get('locale')?.value || 'zh';

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default,
  };
});
