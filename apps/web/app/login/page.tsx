"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { isAuthenticated, useCurrentUser, useLogin } from "@/service/auth-service";
import { siteConfigAtom } from "@/store/user-store";
import { Button } from "@ragtop-web/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ragtop-web/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { useAtomValue } from "jotai";
import { LogIn } from "lucide-react";
import Image from "next/image";

/**
 * 登录页面组件
 */
export default function LoginPage() {
  const router = useRouter();
  const theme = useTheme();
  const isDark = theme.resolvedTheme === "dark";
  const t = useTranslations("login");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const signinMutation = useLogin();
  const currentUserMutation = useCurrentUser();
  const isAuthed = isAuthenticated();
  const [mounted, setMounted] = useState(false);
  const siteConfig = useAtomValue(siteConfigAtom);

  // 表单验证模式
  const formSchema = z.object({
    username: z.string().min(2, t("usernameMin")),
    password: z.string(),
  });

  type FormValues = z.infer<typeof formSchema>;

  useEffect(() => {
    setMounted(true);
  }, []);

  // 检查是否已登录
  useEffect(() => {
    if (isAuthed) {
      router.push("/");
    }
  }, [router, isAuthed]);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  // 处理登录
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true);
    setError("");

    signinMutation.mutate(values, {
      onSuccess: () => {
        // 登录成功后，调用useCurrentUser获取完整用户信息
        currentUserMutation.mutate(undefined, {
          onSuccess: () => {
            // 获取用户信息成功后，跳转到home页面
            router.push("/");
          },
          onError: (error) => {
            // console.error("获取用户信息失败:", error)
            // 即使获取用户信息失败，也跳转到home页面
            router.push("/");
          },
        });
      },
      onError: (error) => {
        console.log(error);
        setError("登录失败，请重试");
        setIsLoading(false);
      },
    });
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-4 p-4">
      {siteConfig && (
        <Image
          src={
            isDark && mounted
              ? `/frontend-api/image-proxy?key=${siteConfig?.dark_logo}`
              : `/frontend-api/image-proxy?key=${siteConfig?.logo}`
          }
          alt="logo"
          width={140}
          height={40}
          unoptimized
        />
      )}
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-xl font-bold">{t("title")}</CardTitle>
          <CardDescription>{t("description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-destructive/15 text-destructive mb-4 rounded-md p-3 text-sm">
              {error}
            </div>
          )}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("username")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("usernameMin")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("password")}</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder={t("passwordPlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    {t("submitting")}
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <LogIn className="h-4 w-4" />
                    {t("title")}
                  </span>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      {siteConfig?.name === "MAXIR AI" && (
        <Image src={"/logo/ucloud-grey.svg"} alt="logo" width={140} height={40} className="mt-4" />
      )}
    </div>
  );
}
