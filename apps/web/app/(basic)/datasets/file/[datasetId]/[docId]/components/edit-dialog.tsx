"use client";

import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogBody,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog";
import { Input } from "@ragtop-web/ui/components/input";
import { Textarea } from "@ragtop-web/ui/components/textarea";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

export default function EditDialog({
  open,
  onClose,
  semantic_name,
  semantic_comment,
  onSave,
  type = "worksheet",
}: {
  open: boolean;
  onClose: () => void;
  semantic_name: string | undefined;
  semantic_comment: string | undefined;
  onSave: (semantic_name: string | undefined, semantic_comment: string | undefined) => void;
  type?: "worksheet" | "column";
}) {
  const t = useTranslations("datasets.file.worksheet");
  const [aliasValue, setAliasValue] = useState("");
  const [commentValue, setCommentValue] = useState("");

  // label/placeholder/help 文本根据 type 切换
  const aliasLabel = type === "worksheet" ? t("sheetAlias") : t("columnName");
  const aliasPlaceholder =
    type === "worksheet" ? t("sheetAliasPlaceholder") : t("columnNamePlaceholder");
  const aliasHelp = type === "worksheet" ? t("sheetAliasHelp") : t("columnNameHelp");
  const commentLabel = type === "worksheet" ? t("sheetDescription") : t("columnDescription");
  const commentPlaceholder =
    type === "worksheet" ? t("sheetDescriptionPlaceholder") : t("columnDescriptionPlaceholder");
  const commentHelp = type === "worksheet" ? t("sheetCommentHelp") : t("columnDescriptionHelp");

  // 处理保存
  const handleSave = () => {
    onSave(aliasValue.trim() || undefined, commentValue.trim() || undefined);
  };

  // 重置表单
  const handleClose = () => {
    setAliasValue("");
    setCommentValue("");
    onClose();
  };

  useEffect(() => {
    if (open) {
      setAliasValue(semantic_name || "");
      setCommentValue(semantic_comment || "");
    }
  }, [open, semantic_name, semantic_comment]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t("edit")}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="semantic_name" className="block text-sm font-medium">
                {aliasLabel}
              </label>
              <Input
                id="semantic_name"
                placeholder={aliasPlaceholder}
                value={aliasValue}
                onChange={(e) => setAliasValue(e.target.value)}
              />
              <p className="text-muted-foreground text-xs">{aliasHelp}</p>
            </div>

            <div className="space-y-2">
              <label htmlFor="semantic_comment" className="block text-sm font-medium">
                {commentLabel}
              </label>
              <Textarea
                id="semantic_comment"
                placeholder={commentPlaceholder}
                value={commentValue}
                onChange={(e) => setCommentValue(e.target.value)}
                rows={4}
              />
              <p className="text-muted-foreground text-xs">{commentHelp}</p>
            </div>
          </div>
        </DialogBody>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {t("cancel")}
          </Button>
          <Button onClick={handleSave}>{t("save")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
