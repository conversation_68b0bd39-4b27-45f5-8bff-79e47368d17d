# 文件选择抽屉组件国际化完成报告

## 概述

已完成两个 `file-selection-drawer.tsx` 组件的国际化翻译工作，将所有硬编码的中文文本替换为翻译键。

## 修改的文件

### 1. 组件文件
- `apps/web/app/(basic)/knowledge-base/components/file-selection-drawer.tsx` - 知识库文件选择抽屉
- `apps/web/app/(basic)/datasets/file/[datasetId]/components/file-selection-drawer.tsx` - 数据集文件选择抽屉

### 2. 翻译文件
- `apps/web/messages/zh.json` - 添加了新的翻译键
- `apps/web/messages/en.json` - 添加了对应的英文翻译

## 新增的翻译键

### 知识库文件选择 (`knowledgeBase.fileSelection`)

| 翻译键 | 中文 | 英文 |
|--------|------|------|
| `selectFiles` | 选择文件 | Select Files |
| `addToKnowledgeBase` | 添加到知识库 | Add to Knowledge Base |
| `cancel` | 取消 | Cancel |
| `selectedFilesCount` | 已选择 {count} 个文件 | Selected {count} files |
| `fileList` | 文件列表 | File List |
| `filesCount` | {count} 个文件 | {count} files |
| `unlinkCount` | 未被链接数 | Unlinked count |
| `createTime` | 创建时间 | Create time |
| `size` | 大小 | Size |

### 数据集文件选择 (`datasets.fileSelection`)

| 翻译键 | 中文 | 英文 |
|--------|------|------|
| `title` | 选择文件 | Select Files |
| `addToDataset` | 添加到文件数据集 | Add to File Dataset |
| `cancel` | 取消 | Cancel |
| `selectedFilesCount` | 已选择 {count} 个文件 | Selected {count} files |
| `maxFilesLimit` | 最多添加{max}个文件({current}/{max}) | Maximum {max} files ({current}/{max}) |
| `maxFilesExceeded` | 文件数据集关联的文件数量最大{max}个 | File dataset can associate maximum {max} files |
| `fileList` | 文件列表 | File List |
| `filesCount` | {count} 个文件 | {count} files |
| `rootDirectory` | 根目录 | Root Directory |
| `searchPlaceholder` | 搜索文件... | Search files... |
| `emptyText` | 未找到匹配的文件 | No matching files found |
| `loadingText` | 加载中... | Loading... |
| `endText` | 已显示所有文件 | All files loaded |
| `unlinkCount` | 未被链接数 | Unlinked count |
| `createTime` | 创建时间 | Create time |
| `size` | 大小 | Size |

## 替换的硬编码文本

### 1. 知识库文件选择抽屉

**抽屉标题：**
```typescript
// 之前
title={`选择文件 - ${knowledgeBaseName}`}

// 之后
title={`${t("selectFiles")} - ${knowledgeBaseName}`}
```

**按钮文本：**
```typescript
// 之前
<Button variant="outline" onClick={handleCancel}>取消</Button>
<Button onClick={handleConfirm}>添加到知识库</Button>

// 之后
<Button variant="outline" onClick={handleCancel}>{t("cancel")}</Button>
<Button onClick={handleConfirm}>{t("addToKnowledgeBase")}</Button>
```

**文件信息显示：**
```typescript
// 之前
`未被链接数: ${file.unlink_file_num} | 创建时间: ${formatDateTime(new Date(file.create_time))}`
`大小: ${formatFileSize(file.size)} | 创建时间: ${formatDateTime(new Date(file.create_time))}`

// 之后
`${t("unlinkCount")}: ${file.unlink_file_num} | ${t("createTime")}: ${formatDateTime(new Date(file.create_time))}`
`${t("size")}: ${formatFileSize(file.size)} | ${t("createTime")}: ${formatDateTime(new Date(file.create_time))}`
```

### 2. 数据集文件选择抽屉

**错误消息：**
```typescript
// 之前
title: `文件数据集关联的文件数量最大${MAX_REFERENCE_NUM}个`

// 之后
title: t("maxFilesExceeded", { max: MAX_REFERENCE_NUM })
```

**文件限制提示：**
```typescript
// 之前
最多添加{MAX_REFERENCE_NUM}个文件({hasReferenceNum + selectedFileIds.length}/{MAX_REFERENCE_NUM})

// 之后
{t("maxFilesLimit", { 
  max: MAX_REFERENCE_NUM, 
  current: hasReferenceNum + selectedFileIds.length 
})}
```

### 3. 共同更新的内容

**根目录名称：**
```typescript
// 之前
{ id: null, name: "根目录", path: "/" }

// 之后
{ id: null, name: t("rootDirectory"), path: "/" }
```

**SelectionList 属性：**
```typescript
// 之前
searchPlaceholder="搜索文件..."
emptyText="未找到匹配的文件"
loadingText="加载中..."
endText="已显示所有文件"

// 之后
searchPlaceholder={t("searchPlaceholder")}
emptyText={t("emptyText")}
loadingText={t("loadingText")}
endText={t("endText")}
```

## 技术实现

### 1. 导入翻译函数
```typescript
import { useTranslations } from "next-intl";
```

### 2. 使用翻译 Hook
```typescript
// 知识库组件
const t = useTranslations("knowledgeBase.fileSelection");

// 数据集组件
const t = useTranslations("datasets.fileSelection");
```

### 3. 参数化翻译
使用了带参数的翻译，支持动态内容：
```typescript
t("selectedFilesCount", { count: selectedFileIds.length })
t("maxFilesLimit", { max: MAX_REFERENCE_NUM, current: hasReferenceNum + selectedFileIds.length })
```

## 验证结果

### 1. 构建测试
- ✅ `npm run build` 成功通过
- ✅ 无 TypeScript 错误
- ✅ 无 ESLint 警告

### 2. 翻译完整性
- ✅ 所有硬编码文本已替换为翻译键
- ✅ 中英文翻译键完整对应
- ✅ 翻译键命名规范一致

### 3. 功能完整性
- ✅ 保持了原有的所有功能
- ✅ 用户交互逻辑未改变
- ✅ 组件状态管理正常

## 使用说明

### 1. 语言切换
两个文件选择抽屉组件现在完全支持中英文切换，所有文本都会根据当前语言设置显示对应的翻译。

### 2. 翻译键位置
- 知识库相关翻译键位于 `knowledgeBase.fileSelection` 命名空间
- 数据集相关翻译键位于 `datasets.fileSelection` 命名空间

### 3. 扩展性
如果需要添加新的语言支持，只需在对应的翻译文件中添加相同的键值对即可。

## 最佳实践遵循

1. **命名规范**：翻译键使用 camelCase 命名，语义清晰
2. **命名空间**：合理使用命名空间避免键名冲突
3. **复用性**：在两个组件间复用了相同的翻译键
4. **一致性**：保持与项目其他部分的翻译风格一致
5. **参数化**：合理使用参数化翻译支持动态内容

## 总结

本次国际化工作成功完成了两个文件选择抽屉组件的所有硬编码文本替换，提升了应用的国际化支持水平。组件现在完全支持多语言切换，为用户提供了更好的本地化体验。
