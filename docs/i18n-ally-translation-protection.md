# i18n-ally 翻译保护配置修复

## 问题描述

之前在使用 i18n-ally 插件的翻译功能时，会出现已有的 `en.json` 文件中的翻译内容被清空的问题。这是由于配置不当导致翻译结果直接覆盖现有内容。

## 问题原因

1. `saveAsCandidates` 设置为 `false`，导致翻译结果直接覆盖现有内容
2. 缺少 `overrideExisting: false` 配置来保护现有翻译
3. 缺少翻译前的确认提示机制

## 修复内容

### 1. 修改 `.vscode/settings.json`

```json
{
  "i18n-ally.translate.saveAsCandidates": true,
  "i18n-ally.translate.overrideExisting": false,
  "i18n-ally.translate.promptBeforeTranslate": true,
  "i18n-ally.keepFulfilled": true,
  "i18n-ally.keepFulfilledPlaceholders": true
}
```

### 2. 修改 `.vscode/i18n-ally.workspace.json`

```json
{
  "projects": [
    {
      "translate": {
        "saveAsCandidates": true,
        "overrideExisting": false,
        "promptBeforeTranslate": true
      }
    }
  ]
}
```

### 3. 修改 `apps/web/.i18nrc.json`

```json
{
  "keepFulfilled": true,
  "keepFulfilledPlaceholders": true,
  "translate": {
    "saveAsCandidates": true,
    "overrideExisting": false,
    "promptBeforeTranslate": true
  }
}
```

### 4. 修改 `.vscode/i18n-ally.config.js`

```javascript
module.exports = {
  translate: {
    saveAsCandidates: true,
    overrideExisting: false,
    promptBeforeTranslate: true,
    promptSource: true
  }
};
```

## 配置说明

| 配置项 | 值 | 说明 |
|--------|----|----|
| `saveAsCandidates` | `true` | 翻译结果作为候选项保存，不直接覆盖现有内容 |
| `overrideExisting` | `false` | 不覆盖已有的翻译内容 |
| `promptBeforeTranslate` | `true` | 翻译前显示确认提示 |
| `keepFulfilled` | `true` | 保持已完成的翻译不被覆盖 |
| `keepFulfilledPlaceholders` | `true` | 保持已完成的占位符不被覆盖 |
| `promptSource` | `true` | 提示翻译源语言 |

## 使用方法

### 1. 重新加载配置

修复配置后，需要重新加载 i18n-ally 插件：

1. 重启 VSCode
2. 或者按 `Ctrl+Shift+P`，运行 `i18n-ally: Reload`

### 2. 安全翻译流程

现在使用翻译功能时：

1. **翻译前确认**：会显示确认对话框，询问是否要翻译
2. **候选项模式**：翻译结果会作为候选项显示，而不是直接覆盖
3. **手动确认**：需要手动选择和确认翻译结果
4. **保护现有内容**：已有的翻译内容不会被自动覆盖

### 3. 验证配置

运行测试脚本验证配置是否正确：

```bash
node scripts/test-i18n-ally-config.js
```

## 最佳实践

1. **备份翻译文件**：在进行大量翻译前，建议备份现有的翻译文件
2. **逐步翻译**：建议分批次翻译，避免一次性翻译大量内容
3. **人工审核**：自动翻译结果需要人工审核和调整
4. **版本控制**：使用 Git 跟踪翻译文件的变更

## 故障排除

### 如果翻译功能仍然覆盖内容

1. 检查所有配置文件是否正确修改
2. 重启 VSCode 并重新加载 i18n-ally
3. 检查 i18n-ally 插件版本是否最新
4. 查看 VSCode 输出面板中的 i18n-ally 日志

### 如果翻译功能不工作

1. 确认 i18n-ally 插件已正确安装和启用
2. 检查翻译引擎配置（Google、DeepL）
3. 确认网络连接正常
4. 查看插件设置中的错误信息

## 相关文件

- `.vscode/settings.json` - VSCode 全局设置
- `.vscode/i18n-ally.workspace.json` - 工作区特定设置
- `.vscode/i18n-ally.config.js` - i18n-ally 配置文件
- `apps/web/.i18nrc.json` - 项目级别配置
- `scripts/test-i18n-ally-config.js` - 配置验证脚本

## 更新日志

- **2025-08-05**: 修复翻译覆盖问题，添加翻译保护配置
- **2025-08-05**: 添加配置验证脚本和文档
