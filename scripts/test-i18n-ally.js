#!/usr/bin/env node

/**
 * 测试 i18n-ally 配置是否正确
 */

import fs from 'fs';
import path from 'path';

function main() {
  console.log('🔍 测试 i18n-ally 配置...\n');
  
  // 检查配置文件是否存在
  const configFiles = [
    '.vscode/settings.json',
    '.vscode/i18n-ally-custom-framework.yml',
    '.vscode/i18n-ally.workspace.json',
    'apps/web/.i18nrc.json',
    'apps/web/i18n.ts'
  ];
  
  console.log('📁 检查配置文件:');
  configFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`   ${exists ? '✅' : '❌'} ${file}`);
  });
  console.log('');
  
  // 检查翻译文件
  const translationFiles = [
    'apps/web/messages/zh.json',
    'apps/web/messages/en.json'
  ];
  
  console.log('📝 检查翻译文件:');
  translationFiles.forEach(file => {
    const exists = fs.existsSync(file);
    if (exists) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const parsed = JSON.parse(content);
        const keyCount = countKeys(parsed);
        console.log(`   ✅ ${file} (${keyCount} 个键)`);
      } catch (error) {
        console.log(`   ❌ ${file} (JSON 解析错误: ${error.message})`);
      }
    } else {
      console.log(`   ❌ ${file} (文件不存在)`);
    }
  });
  console.log('');
  
  // 检查关键配置
  try {
    const vscodeSettings = JSON.parse(fs.readFileSync('.vscode/settings.json', 'utf8'));
    console.log('⚙️  关键配置检查:');
    
    const keyConfigs = [
      'i18n-ally.localesPaths',
      'i18n-ally.pathMatcher',
      'i18n-ally.keystyle',
      'i18n-ally.sourceLanguage',
      'i18n-ally.enabledFrameworks',
      'i18n-ally.editor.autoSave',
      'i18n-ally.translate.fallbackMissing',
      'i18n-ally.extract.autoDetect'
    ];
    
    keyConfigs.forEach(key => {
      const value = vscodeSettings[key];
      console.log(`   ${value !== undefined ? '✅' : '❌'} ${key}: ${JSON.stringify(value)}`);
    });
    console.log('');
  } catch (error) {
    console.log(`❌ 无法读取 VSCode 设置: ${error.message}\n`);
  }
  
  // 检查 package.json 中的 next-intl 依赖
  try {
    const packageJson = JSON.parse(fs.readFileSync('apps/web/package.json', 'utf8'));
    const hasNextIntl = packageJson.dependencies?.['next-intl'] || packageJson.devDependencies?.['next-intl'];
    console.log(`📦 next-intl 依赖: ${hasNextIntl ? '✅ ' + hasNextIntl : '❌ 未找到'}`);
  } catch (error) {
    console.log(`❌ 无法读取 package.json: ${error.message}`);
  }
  
  console.log('\n🎯 配置建议:');
  console.log('   1. 确保 VSCode 中安装了 i18n-ally 插件');
  console.log('   2. 重启 VSCode 以加载新的配置');
  console.log('   3. 在 VSCode 中按 Ctrl+Shift+P，运行 "i18n-ally: Reload"');
  console.log('   4. 检查 VSCode 状态栏是否显示 i18n-ally 图标');
  console.log('   5. 尝试在代码中点击翻译键，看是否能正确跳转到翻译文件');
}

function countKeys(obj, prefix = '') {
  let count = 0;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        count += countKeys(obj[key], prefix ? `${prefix}.${key}` : key);
      } else {
        count++;
      }
    }
  }
  return count;
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main };
