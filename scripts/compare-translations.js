#!/usr/bin/env node

/**
 * 比较中英文翻译文件，找出缺失的键
 */

import fs from 'fs';
import path from 'path';

// 加载翻译文件
function loadTranslations(locale) {
  const filePath = path.join(process.cwd(), 'apps/web/messages', `${locale}.json`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${locale} translations:`, error.message);
    return {};
  }
}

// 获取所有嵌套键的路径
function getAllKeys(obj, prefix = '') {
  const keys = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        keys.push(...getAllKeys(obj[key], fullKey));
      } else {
        keys.push(fullKey);
      }
    }
  }
  
  return keys;
}

// 获取嵌套值
function getNestedValue(obj, keyPath) {
  return keyPath.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// 主函数
function main() {
  console.log('🔍 比较中英文翻译文件...\n');
  
  // 加载翻译文件
  const zhTranslations = loadTranslations('zh');
  const enTranslations = loadTranslations('en');
  
  // 获取所有键
  const zhKeys = new Set(getAllKeys(zhTranslations));
  const enKeys = new Set(getAllKeys(enTranslations));
  
  console.log(`📊 统计信息:`);
  console.log(`   中文键数量: ${zhKeys.size}`);
  console.log(`   英文键数量: ${enKeys.size}\n`);
  
  // 找出缺失的键
  const missingInEn = [];
  const missingInZh = [];
  
  for (const key of zhKeys) {
    if (!enKeys.has(key)) {
      missingInEn.push(key);
    }
  }
  
  for (const key of enKeys) {
    if (!zhKeys.has(key)) {
      missingInZh.push(key);
    }
  }
  
  // 报告结果
  if (missingInEn.length > 0) {
    console.log(`❌ 英文翻译缺失 (${missingInEn.length} 个):`);
    missingInEn.sort().forEach(key => {
      const zhValue = getNestedValue(zhTranslations, key);
      console.log(`   - ${key}: "${zhValue}"`);
    });
    console.log('');
  }
  
  if (missingInZh.length > 0) {
    console.log(`❌ 中文翻译缺失 (${missingInZh.length} 个):`);
    missingInZh.sort().forEach(key => {
      const enValue = getNestedValue(enTranslations, key);
      console.log(`   - ${key}: "${enValue}"`);
    });
    console.log('');
  }
  
  if (missingInEn.length === 0 && missingInZh.length === 0) {
    console.log('✅ 所有翻译键都匹配！');
  } else {
    console.log(`📋 总结:`);
    console.log(`   需要添加到英文: ${missingInEn.length} 个`);
    console.log(`   需要添加到中文: ${missingInZh.length} 个`);
  }
  
  // 按命名空间分组显示缺失的键
  if (missingInEn.length > 0) {
    console.log('\n📂 按命名空间分组 (英文缺失):');
    const grouped = {};
    missingInEn.forEach(key => {
      const namespace = key.split('.')[0];
      if (!grouped[namespace]) grouped[namespace] = [];
      grouped[namespace].push(key);
    });
    
    Object.keys(grouped).sort().forEach(namespace => {
      console.log(`   ${namespace}: ${grouped[namespace].length} 个键`);
      grouped[namespace].forEach(key => {
        console.log(`     - ${key.replace(namespace + '.', '')}`);
      });
    });
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main };
