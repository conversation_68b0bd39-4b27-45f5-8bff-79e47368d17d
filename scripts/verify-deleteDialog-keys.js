#!/usr/bin/env node

/**
 * 验证 deleteDialog 翻译键修复
 */

import fs from "fs";

function main() {
  console.log("🔍 验证 deleteDialog 翻译键修复...\n");

  // 加载翻译文件
  const zhMessages = JSON.parse(fs.readFileSync("apps/web/messages/zh.json", "utf8"));
  const enMessages = JSON.parse(fs.readFileSync("apps/web/messages/en.json", "utf8"));

  // 测试的翻译键
  const testKeys = [
    "knowledgeBase.details.deleteDialog.title",
    "knowledgeBase.details.deleteDialog.description",
    "knowledgeBase.details.deleteDialog.singleFile",
    "knowledgeBase.details.deleteDialog.multipleFiles",
  ];

  console.log("📋 检查缺失的翻译键:\n");

  let allPassed = true;

  testKeys.forEach((key) => {
    const zhValue = key.split(".").reduce((obj, k) => obj?.[k], zhMessages);
    const enValue = key.split(".").reduce((obj, k) => obj?.[k], enMessages);

    const status = zhValue && enValue ? "✅" : "❌";
    console.log(`${status} ${key}`);

    if (zhValue) {
      console.log(`   zh: "${zhValue}"`);
    } else {
      console.log(`   zh: ❌ 未找到`);
      allPassed = false;
    }

    if (enValue) {
      console.log(`   en: "${enValue}"`);
    } else {
      console.log(`   en: ❌ 未找到`);
      allPassed = false;
    }

    console.log("");
  });

  if (allPassed) {
    console.log("🎉 所有 deleteDialog 翻译键都已修复！");
  } else {
    console.log("⚠️  仍有翻译键缺失，需要进一步修复。");
  }

  // 额外检查其他 deleteDialog 部分
  console.log("\n📊 其他 deleteDialog 部分状态:");

  const otherSections = ["datasets.deleteDialog", "knowledgeBase.deleteDialog"];

  otherSections.forEach((section) => {
    const zhSection = section.split(".").reduce((obj, k) => obj?.[k], zhMessages);
    const enSection = section.split(".").reduce((obj, k) => obj?.[k], enMessages);

    const zhComplete = zhSection && Object.values(zhSection).every((v) => v !== "");
    const enComplete = enSection && Object.values(enSection).every((v) => v !== "");

    console.log(`   ${section}: zh ${zhComplete ? "✅" : "❌"} | en ${enComplete ? "✅" : "❌"}`);
  });
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main };
