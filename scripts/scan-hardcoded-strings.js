#!/usr/bin/env node

/**
 * 扫描硬编码的中文字符串
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// 中文字符正则表达式
const chineseRegexes = [
  // 字符串字面量中的中文
  /["'`]([^"'`]*[\u4e00-\u9fff][^"'`]*)["'`]/g,
  // JSX 文本中的中文
  />([^<]*[\u4e00-\u9fff][^<]*)</g,
  // 对象属性中的中文
  /:\s*["'`]([^"'`]*[\u4e00-\u9fff][^"'`]*)["'`]/g,
  // 模板字符串中的中文
  /`([^`]*[\u4e00-\u9fff][^`]*)`/g
];

// 忽略的模式
const ignorePatterns = [
  /console\.(log|error|warn|info)/,
  /\/\*[\s\S]*?\*\//,
  /\/\/.*$/,
  /import.*from/,
  /export.*from/,
  /useTranslations\(/,
  /t\(/
];

// 扫描文件中的中文字符串
function scanFileForChineseStrings(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = [];
    
    // 按行分割内容
    const lines = content.split('\n');
    
    lines.forEach((line, lineIndex) => {
      // 跳过注释和导入语句
      if (ignorePatterns.some(pattern => pattern.test(line))) {
        return;
      }
      
      // 检查每个正则表达式
      chineseRegexes.forEach(regex => {
        let match;
        while ((match = regex.exec(line)) !== null) {
          const chineseText = match[1] || match[0];
          
          // 过滤掉纯英文或数字
          if (/[\u4e00-\u9fff]/.test(chineseText)) {
            results.push({
              line: lineIndex + 1,
              text: chineseText.trim(),
              context: line.trim()
            });
          }
        }
        regex.lastIndex = 0; // 重置正则表达式
      });
    });
    
    return results;
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
    return [];
  }
}

// 主函数
async function main() {
  console.log('🔍 扫描硬编码中文字符串...\n');
  
  // 目标文件模式
  const targetPatterns = [
    'apps/web/app/(basic)/datasets/**/*.{ts,tsx}',
    'apps/web/app/(basic)/knowledge-base/**/*.{ts,tsx}',
    'apps/web/components/**/*.{ts,tsx}'
  ];
  
  const allFiles = [];
  for (const pattern of targetPatterns) {
    const files = await glob(pattern, {
      ignore: ['**/node_modules/**', '**/dist/**', '**/.next/**']
    });
    allFiles.push(...files);
  }
  
  console.log(`📁 扫描 ${allFiles.length} 个文件...\n`);
  
  const fileResults = new Map();
  let totalStrings = 0;
  
  // 扫描每个文件
  for (const file of allFiles) {
    const strings = scanFileForChineseStrings(file);
    if (strings.length > 0) {
      fileResults.set(file, strings);
      totalStrings += strings.length;
    }
  }
  
  console.log(`🔤 发现 ${totalStrings} 个硬编码中文字符串\n`);
  
  // 按文件分组显示结果
  if (fileResults.size > 0) {
    console.log('📋 详细结果:\n');
    
    Array.from(fileResults.entries())
      .sort((a, b) => b[1].length - a[1].length)
      .forEach(([file, strings]) => {
        console.log(`📄 ${file} (${strings.length} 个字符串):`);
        
        strings.forEach(({ line, text, context }) => {
          console.log(`   第${line}行: "${text}"`);
          console.log(`   上下文: ${context.substring(0, 80)}${context.length > 80 ? '...' : ''}`);
          console.log('');
        });
        
        console.log('');
      });
  } else {
    console.log('✅ 没有发现硬编码中文字符串！');
  }
  
  // 按类型分组统计
  const typeStats = {};
  fileResults.forEach((strings, file) => {
    const type = file.includes('/datasets/') ? 'datasets' :
                 file.includes('/knowledge-base/') ? 'knowledge-base' :
                 file.includes('/components/') ? 'components' : 'other';
    
    if (!typeStats[type]) typeStats[type] = 0;
    typeStats[type] += strings.length;
  });
  
  console.log('📊 按类型统计:');
  Object.entries(typeStats).forEach(([type, count]) => {
    console.log(`   ${type}: ${count} 个字符串`);
  });
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main };
