#!/usr/bin/env node

/**
 * 测试 i18n-ally 配置是否正确设置以防止翻译覆盖
 */

import fs from "fs";

function main() {
  console.log("🔍 检查 i18n-ally 翻译保护配置...\n");

  const configFiles = [
    {
      path: ".vscode/settings.json",
      type: "json",
      checkKeys: [
        "i18n-ally.translate.saveAsCandidates",
        "i18n-ally.translate.overrideExisting",
        "i18n-ally.translate.promptBeforeTranslate",
        "i18n-ally.keepFulfilled",
        "i18n-ally.keepFulfilledPlaceholders",
      ],
    },
    {
      path: ".vscode/i18n-ally.workspace.json",
      type: "json",
      checkKeys: [
        "projects[0].translate.saveAsCandidates",
        "projects[0].translate.overrideExisting",
        "projects[0].translate.promptBeforeTranslate",
      ],
    },
    {
      path: "apps/web/.i18nrc.json",
      type: "json",
      checkKeys: [
        "translate.saveAsCandidates",
        "translate.overrideExisting",
        "translate.promptBeforeTranslate",
        "keepFulfilled",
        "keepFulfilledPlaceholders",
      ],
    },
  ];

  let allPassed = true;

  for (const config of configFiles) {
    console.log(`📁 检查 ${config.path}:`);

    if (!fs.existsSync(config.path)) {
      console.log(`   ❌ 文件不存在`);
      allPassed = false;
      continue;
    }

    try {
      const content = JSON.parse(fs.readFileSync(config.path, "utf8"));

      for (const key of config.checkKeys) {
        const value = getNestedValue(content, key);
        const expected = getExpectedValue(key);

        if (value === expected) {
          console.log(`   ✅ ${key}: ${value}`);
        } else {
          console.log(`   ❌ ${key}: ${value} (期望: ${expected})`);
          allPassed = false;
        }
      }
    } catch (error) {
      console.log(`   ❌ 解析失败: ${error.message}`);
      allPassed = false;
    }

    console.log("");
  }

  // 检查翻译文件是否存在且有内容
  console.log("📝 检查翻译文件:");
  const translationFiles = ["apps/web/messages/zh.json", "apps/web/messages/en.json"];

  for (const file of translationFiles) {
    if (fs.existsSync(file)) {
      try {
        const content = JSON.parse(fs.readFileSync(file, "utf8"));
        const keyCount = countKeys(content);
        console.log(`   ✅ ${file}: ${keyCount} 个翻译键`);
      } catch (error) {
        console.log(`   ❌ ${file}: 解析失败`);
        allPassed = false;
      }
    } else {
      console.log(`   ❌ ${file}: 文件不存在`);
      allPassed = false;
    }
  }

  console.log("\n🎯 配置说明:");
  console.log("   • saveAsCandidates: true - 翻译结果作为候选项保存，不直接覆盖");
  console.log("   • overrideExisting: false - 不覆盖已有翻译");
  console.log("   • promptBeforeTranslate: true - 翻译前提示确认");
  console.log("   • keepFulfilled: true - 保持已完成的翻译");
  console.log("   • keepFulfilledPlaceholders: true - 保持已完成的占位符");

  console.log("\n📋 使用建议:");
  console.log("   1. 重启 VSCode 以加载新配置");
  console.log('   2. 在 VSCode 中按 Ctrl+Shift+P，运行 "i18n-ally: Reload"');
  console.log("   3. 使用翻译功能时会看到确认提示");
  console.log("   4. 翻译结果会作为候选项显示，需要手动确认");

  if (allPassed) {
    console.log("\n🎉 所有配置检查通过！翻译保护已启用。");
  } else {
    console.log("\n⚠️  部分配置需要修复，请检查上述错误。");
  }
}

function getNestedValue(obj, path) {
  // 处理带连字符的键名
  if (path.includes("-")) {
    return obj[path];
  }

  return path
    .split(/[\.\[\]]/)
    .filter(Boolean)
    .reduce((current, key) => {
      if (current && typeof current === "object") {
        return current[key];
      }
      return undefined;
    }, obj);
}

function getExpectedValue(key) {
  if (key.includes("saveAsCandidates")) return true;
  if (key.includes("overrideExisting")) return false;
  if (key.includes("promptBeforeTranslate")) return true;
  if (key.includes("keepFulfilled")) return true;
  if (key.includes("keepFulfilledPlaceholders")) return true;
  return undefined;
}

function countKeys(obj, prefix = "") {
  let count = 0;
  for (const key in obj) {
    if (typeof obj[key] === "object" && obj[key] !== null) {
      count += countKeys(obj[key], prefix + key + ".");
    } else {
      count++;
    }
  }
  return count;
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
