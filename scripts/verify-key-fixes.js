#!/usr/bin/env node

/**
 * 验证关键翻译键修复
 */

import fs from 'fs';
import path from 'path';

// 加载翻译文件
function loadTranslations(locale) {
  const filePath = path.join(process.cwd(), 'apps/web/messages', `${locale}.json`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${locale} translations:`, error.message);
    return {};
  }
}

// 获取嵌套值
function getNestedValue(obj, keyPath) {
  return keyPath.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// 主函数
function main() {
  console.log('🔍 验证关键翻译键修复...\n');
  
  // 加载翻译文件
  const zhTranslations = loadTranslations('zh');
  const enTranslations = loadTranslations('en');
  
  // 测试关键的翻译键
  const criticalKeys = [
    'sidebar.user.changePassword',
    'sidebar.user.logout',
    'sidebar.user.defaultUser',
    'sidebar.agent.myAgent',
    'sidebar.agent.teamAgent',
    'sidebar.agent.createSuccess',
    'agent.common.success',
    'agent.common.failed',
    'agent.common.confirm',
    'agent.common.cancel',
    'agent.delete.confirmButton',
    'agent.delete.cancelButton',
    'agent.batchDelete.confirmButton',
    'agent.batchDelete.cancelButton',
    'agent.clear.confirmButton',
    'agent.clear.cancelButton',
    'agent.form.prompt',
    'agent.form.userPrompt',
    'agent.form.assistantPrompt',
    'agent.form.flexibility',
    'agent.form.precise',
    'agent.form.balanced',
    'agent.form.creative',
    'agent.details.actions',
    'agent.details.editAgent',
    'agent.details.deleteAgent',
    'knowledgeBase.addKnowledgeBase',
    'knowledgeBase.emptyMessage',
    'knowledgeBase.form.sliceMethod',
    'knowledgeBase.form.descriptionRequired',
    'team.name',
    'team.email',
    'team.status',
    'team.active',
    'team.inactive',
    'team.makeAdmin',
    'team.removeAdmin',
    'team.removeMember',
    'team.messages.removeSuccess',
    'team.messages.removeFailed',
    'model.save',
    'model.saving',
    'model.saveSuccess',
    'model.saveFailed'
  ];
  
  console.log('📋 检查关键翻译键:\n');
  
  let allPassed = true;
  let fixedCount = 0;
  
  criticalKeys.forEach(key => {
    const zhValue = getNestedValue(zhTranslations, key);
    const enValue = getNestedValue(enTranslations, key);
    
    const zhStatus = zhValue !== undefined ? '✅' : '❌';
    const enStatus = enValue !== undefined ? '✅' : '❌';
    
    console.log(`${zhStatus} ${enStatus} ${key}`);
    
    if (zhValue !== undefined) {
      console.log(`   zh: "${zhValue}"`);
    }
    if (enValue !== undefined) {
      console.log(`   en: "${enValue}"`);
      if (zhValue !== undefined) {
        fixedCount++;
      }
    }
    
    if (zhValue === undefined || enValue === undefined) {
      allPassed = false;
    }
    
    console.log('');
  });
  
  console.log(`📊 统计结果:`);
  console.log(`   测试键总数: ${criticalKeys.length}`);
  console.log(`   完全匹配: ${fixedCount}`);
  console.log(`   修复率: ${Math.round((fixedCount / criticalKeys.length) * 100)}%`);
  
  if (allPassed) {
    console.log('\n🎉 所有关键翻译键都已修复！');
  } else {
    console.log('\n⚠️  仍有部分翻译键需要修复。');
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main };
