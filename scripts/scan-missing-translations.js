#!/usr/bin/env node

/**
 * 扫描所有缺失的翻译键
 * 遍历所有使用 useTranslations 的文件，找出缺失的翻译键
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// 加载翻译文件
function loadTranslations(locale) {
  const filePath = path.join(process.cwd(), 'apps/web/messages', `${locale}.json`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${locale} translations:`, error.message);
    return {};
  }
}

// 获取嵌套值
function getNestedValue(obj, keyPath) {
  return keyPath.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// 设置嵌套值
function setNestedValue(obj, keyPath, value) {
  const keys = keyPath.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

// 扫描文件中的翻译键
function scanFileForTranslationKeys(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const keys = new Set();
    
    // 匹配 useTranslations("namespace") 
    const namespaceMatches = content.match(/useTranslations\s*\(\s*["']([^"']+)["']\s*\)/g);
    const namespaces = namespaceMatches ? 
      namespaceMatches.map(match => match.match(/["']([^"']+)["']/)[1]) : [];
    
    // 匹配 t("key") 调用
    const tCallMatches = content.match(/\bt\s*\(\s*["']([^"']+)["']\s*\)/g);
    if (tCallMatches) {
      tCallMatches.forEach(match => {
        const key = match.match(/["']([^"']+)["']/)[1];
        namespaces.forEach(namespace => {
          keys.add(`${namespace}.${key}`);
        });
      });
    }
    
    return Array.from(keys);
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
    return [];
  }
}

// 主函数
async function main() {
  console.log('🔍 扫描所有缺失的翻译键...\n');
  
  // 加载翻译文件
  const zhTranslations = loadTranslations('zh');
  const enTranslations = loadTranslations('en');
  
  // 扫描所有 TypeScript/JavaScript 文件
  const files = await glob('apps/web/**/*.{ts,tsx,js,jsx}', {
    ignore: ['**/node_modules/**', '**/dist/**', '**/.next/**']
  });
  
  const allKeys = new Set();
  const fileKeyMap = new Map();
  
  // 扫描每个文件
  for (const file of files) {
    const keys = scanFileForTranslationKeys(file);
    if (keys.length > 0) {
      fileKeyMap.set(file, keys);
      keys.forEach(key => allKeys.add(key));
    }
  }
  
  console.log(`📁 扫描了 ${files.length} 个文件`);
  console.log(`🔑 发现 ${allKeys.size} 个翻译键\n`);
  
  // 检查缺失的翻译键
  const missingInZh = [];
  const missingInEn = [];
  
  for (const key of allKeys) {
    const zhValue = getNestedValue(zhTranslations, key);
    const enValue = getNestedValue(enTranslations, key);
    
    if (zhValue === undefined) {
      missingInZh.push(key);
    }
    if (enValue === undefined) {
      missingInEn.push(key);
    }
  }
  
  // 报告结果
  console.log('📊 扫描结果:\n');
  
  if (missingInZh.length > 0) {
    console.log(`❌ 中文翻译缺失 (${missingInZh.length} 个):`);
    missingInZh.forEach(key => console.log(`   - ${key}`));
    console.log('');
  }
  
  if (missingInEn.length > 0) {
    console.log(`❌ 英文翻译缺失 (${missingInEn.length} 个):`);
    missingInEn.forEach(key => console.log(`   - ${key}`));
    console.log('');
  }
  
  if (missingInZh.length === 0 && missingInEn.length === 0) {
    console.log('✅ 所有翻译键都存在！');
  }
  
  // 显示使用翻译键最多的文件
  console.log('📈 使用翻译键最多的文件:');
  const sortedFiles = Array.from(fileKeyMap.entries())
    .sort((a, b) => b[1].length - a[1].length)
    .slice(0, 10);
    
  sortedFiles.forEach(([file, keys]) => {
    console.log(`   ${file}: ${keys.length} 个键`);
  });
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main };
