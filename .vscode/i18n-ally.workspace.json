{"version": "1.0.0", "name": "Ragtop Web i18n Configuration", "description": "i18n-ally workspace configuration for ragtop-web monorepo", "projects": [{"name": "web", "rootPath": "apps/web", "localesPaths": ["messages"], "pathMatcher": "{locale}.json", "keystyle": "nested", "sourceLanguage": "zh", "displayLanguage": "zh", "namespace": true, "sortKeys": true, "enabledFrameworks": ["next-intl"], "extract": {"keygeneration": "camelCase", "keyMaxLength": 100, "autoDetect": true, "includeSubDirs": true}, "translate": {"engines": ["google", "deepl"], "fallbackMissing": true, "saveAsCandidates": true, "promptSource": true, "overrideExisting": false, "promptBeforeTranslate": true}, "editor": {"preferEditor": true, "autoSave": true, "autoSaveDelay": 1000}, "usage": {"scanning": "active", "preferredDelimiter": ".", "preferredLanguage": "zh"}, "detection": {"autoDetection": true}, "parsers": {"typescript": {"enabled": true}, "javascript": {"enabled": true}, "json": {"enabled": true}}, "regex": {"usageMatchAppend": ["(?:useTranslations|getTranslations)\\s*\\(\\s*['\"`]([^'\"`.]+(?:\\.[^'\"`.]+)*)['\"`]\\s*\\).*?\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)", "\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"], "key": "(?:['\"`])([\\w\\d\\-_\\.]+)(?:['\"`])"}, "refactorTemplates": [{"source": "{text}", "target": "t('{key}')", "description": "Extract to translation key"}]}]}