# i18n-ally 自定义框架配置
# 用于支持 next-intl 的命名空间功能

id: next-intl-custom
display: Next.js Intl Custom
detection:
  packageJSON:
    - next-intl

languageIds:
  - javascript
  - typescript
  - javascriptreact
  - typescriptreact

# 使用模式匹配
usageMatchRegex:
  # 匹配 useTranslations('namespace').t('key')
  - "useTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\).*?\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
  # 匹配 t('key') 在 useTranslations 上下文中
  - "\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
  # 匹配 getTranslations('namespace')('key')
  - "getTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"

# 重写规则
rewriteRules:
  - from: "useTranslations\\('([^']+)'\\)\\.t\\('([^']+)'\\)"
    to: "$1.$2"

# 检测模式
detectionPatterns:
  - "useTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
  - "getTranslations\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"

# 命名空间支持
namespaceDelimiter: "."
supportNamespace: true

# 键值提取模式
keyMatchReg: "['\"`]([\\w\\d\\-_\\.]+)['\"`]"

# 重构模板
refactorTemplates:
  - source: "{text}"
    target: "t('{key}')"
    description: "Extract to translation key"

# 排除模式
excludePatterns:
  - "node_modules/**"
  - ".next/**"
  - "dist/**"
  - "build/**"

# 垄断模式设置为 false，允许与其他框架共存
monopoly: false

# 启用功能
features:
  - namespace
  - extract
  - refactor
  - usage
