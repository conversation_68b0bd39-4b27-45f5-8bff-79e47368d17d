{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit", "source.removeUnusedImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.rulers": [100], "editor.wordWrap": "off", "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": true, "eslint.codeAction.showDocumentation": {"enable": true}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.preferences.organizeImports": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}}, "[json]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["apps/management/messages"], "i18n-ally.pathMatcher": "{locale}.json", "i18n-ally.keystyle": "nested", "i18n-ally.sourceLanguage": "zh", "i18n-ally.displayLanguage": "zh", "i18n-ally.localeCountryMap": {"zh": "CN", "en": "US"}, "i18n-ally.enabledFrameworks": ["next-intl", ".vscode/i18n-ally-custom-framework.yml"], "i18n-ally.sortKeys": true, "i18n-ally.indent": 2, "i18n-ally.tabStyle": "space", "i18n-ally.namespace": true, "i18n-ally.extract.keyMaxLength": 100, "i18n-ally.keepFulfilled": true, "i18n-ally.keepFulfilledPlaceholders": true, "i18n-ally.analytics.enabled": false, "i18n-ally.usage.scanning": "active", "i18n-ally.usage.preferredDelimiter": ".", "i18n-ally.usage.preferredLanguage": "zh", "i18n-ally.detection.autoDetection": true, "i18n-ally.editor.preferEditor": true, "i18n-ally.translate.engines": ["google", "deepl"], "i18n-ally.translate.fallbackMissing": true, "i18n-ally.translate.saveAsCandidates": true, "i18n-ally.translate.promptSource": true, "i18n-ally.translate.overrideExisting": false, "i18n-ally.translate.promptBeforeTranslate": true, "i18n-ally.refactor.templates": [{"source": "t('{key}')", "target": "t('{key}')"}], "i18n-ally.regex.usageMatchAppend": ["(?:useTranslations|getTranslations)\\s*\\(\\s*['\"`]([^'\"`.]+(?:\\.[^'\"`.]+)*)['\"`]\\s*\\).*?\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)", "\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"], "i18n-ally.regex.key": "(?:['\"`])([\\w\\d\\-_\\.]+)(?:['\"`])", "i18n-ally.dirStructure": "file", "i18n-ally.fullReloadOnChanged": true, "i18n-ally.editor.autoSave": true, "i18n-ally.editor.autoSaveDelay": 1000, "i18n-ally.extract.autoDetect": true, "i18n-ally.extract.keygeneration": "camelCase", "i18n-ally.extract.includeSubDirs": true, "i18n-ally.parsers.babel": {"enabled": true}, "i18n-ally.parsers.typescript": {"enabled": true}, "i18n-ally.parsers.json": {"enabled": true}, "i18n-ally.review.enabled": true, "i18n-ally.review.gutters": true, "i18n-ally.review.removeComments": true, "i18n-ally.extract.parsers.html": {"attributes": ["title", "alt", "placeholder", "aria-label"], "ignoredTags": ["script", "style"]}, "i18n-ally.extract.parsers.typescript": {"stringLiterals": true, "templateLiterals": true}, "i18n-ally.extract.parsers.javascript": {"stringLiterals": true, "templateLiterals": true}, "i18n-ally.extract.parsers.jsx": {"stringLiterals": true, "templateLiterals": true}, "i18n-ally.extract.parsers.tsx": {"stringLiterals": true, "templateLiterals": true}, "i18n-ally.extract.autoDetectHardcodedString": true, "i18n-ally.extract.hardcodedStringPatterns": ["[一-龥]+"], "i18n-ally.extract.ignoredPatterns": ["console\\.log\\(.*[一-龥]+.*\\)", "console\\.error\\(.*[一-龥]+.*\\)", "console\\.warn\\(.*[一-龥]+.*\\)", "//.*[一-龥]+", "/\\*.*[一-龥]+.*\\*/", "import.*[一-龥]+.*", "export.*[一-龥]+.*", "const.*=.*[一-龥]+.*", "let.*=.*[一-龥]+.*", "var.*=.*[一-龥]+.*"], "i18n-ally.extract.includeComments": false, "i18n-ally.extract.includeImports": false, "i18n-ally.includeSubfolders": true, "i18n-ally.extract.ignoredByFiles": {"**/*.test.{js,ts,jsx,tsx}": ["*"], "**/*.spec.{js,ts,jsx,tsx}": ["*"], "**/node_modules/**": ["*"], "**/dist/**": ["*"], "**/.next/**": ["*"], "**/coverage/**": ["*"]}, "[xml]": {"editor.defaultFormatter": "vscode.html-language-features"}}